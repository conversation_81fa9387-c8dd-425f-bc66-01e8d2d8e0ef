local log = LOG.new()
log.level = LOG_LEVELS.helpers
local profile = require('sship.profile')

-- Seeds randomization by current time
math.randomseed(os.time());

local module = {}


------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: random
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


---Get a random number.
---@param mini integer Minimum number.
---@param maxi integer Maximum number.
---@return integer randomNumber
function module.randomNumber(mini, maxi)
    return math.random(mini, maxi)
end


------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: faction related
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---@TODO test this new function
---return true if at least one of the building in the set is found
---@param facStruct factionStruct
---@param buildList table<string> list of building levels to look for
---@return boolean
function module.doesFactionHaveOneBuild(facStruct, buildList)
    for i = 0, facStruct.settlementsNum - 1 do
        local sett = facStruct:getSettlement(i)
        for _, building in pairs(buildList) do
            if sett:buildingPresentMinLevel(building, true) then return true end
        end
    end
    return false
end

--- Check if faction leader is alive. If not consider faction dead.
---@TODO just use faction.deadStatus instead ?
--- @param faction factionStruct
--- @return boolean
function module.isFactionAlive(faction)
	log.trace('isFactionAlive() testing faction : '..faction.localizedName..' ('..faction.name..')')
    ---@TODO do we also need to account for special factions :  teutonic family tree, no tree, hordes ?
    if faction.leader == nil then return false
    else return true end
end

--- returns a table, usefull for historic events
---@param faction factionStruct
---@return table neighbours
function module.getNeighbours(faction)
    local neighbours = {}
    for i = 0, M2TW.campaign.numberOfFactions - 1 do
        local targetFaction = M2TW.campaign:getFactionByOrder(i)
        if faction:isNeighbourFaction(targetFaction) then
            table.insert(neighbours, targetFaction.name)
        end
    end
    return neighbours
end

---Add or subtract turmoil points to all faction's settlements
---Turmoil is adjusted once and the game engine cools it down, little by little every turn
---10 pts for example is a big unrest addition, very difficult for the player to manage it
---@param faction factionStruct
---@param turmoil_pts integer
function module.adjustTurmoil(faction, turmoil_pts)
    local settsNum = faction.settlementsNum
    log.debug("Turmoil modification is: "..turmoil_pts)
    log.debug("Player faction name: "..faction.name)
    log.debug("Player faction settlements: ")
    for i = 0, settsNum - 1 do
        local sett = faction:getSettlement(i)
        log.debug(sett.name)
        if sett.turmoil > 0 then
            sett.turmoil = turmoil_pts + sett.turmoil
        end
        -- following advice of @tescrin discussion on eop discord:
        if sett.turmoil == 0 then
            sett.turmoil = sett.settlementStats.PublicOrderUnrest + turmoil_pts
        end
    end
end


---@TODO test
---@param facStruct factionStruct
---@param levelName string
---@param exactLevel boolean - `true` if we want to count only the exact level, `false` if we want to include all levels above
function module.countBuildingLevel(facStruct, levelName, exactLevel)
    local count = 0
    for i = 0, facStruct.settlementsNum - 1 do
        local sett = facStruct:getSettlement(i)
        if sett:buildingPresentMinLevel(levelName, exactLevel) then
            count = count + 1
        end
    end
    return count
end

------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: tables
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

--- Itinialize a set (table strucured like this: key = true) from a list
--- useful for testing quickly if something is in a list or not:
--- just use if set[element] then ...
---@param list table formatted as a list
---@return table<any, boolean>
function module.listToSet(list)
	local set = {}
	for _, value in ipairs(list) do
		set[value] = true
	end
	return set
end

---Checks if an element is present in a table
---@param table table
---@param element any
---@return boolean
function module.checkTable(table, element)
    log.debug("Helpers: element to find: "..tostring(element))
    for _, value in pairs(table) do
        log.debug("Helpers: value found in the table: "..tostring(value))
        if value == element then
            return true
        end
    end
    return false
end

---get the average value of a table (only process number values)
---@param tab table
function module.getTableAverage(tab)
    local sum = 0
    for _, value in ipairs(tab) do
		if type(value) == 'number' then sum = sum + value end
	end
	return sum / #tab
end

module.characterTypeToString = {
    [0] = 'spy',
    [1] = 'assassin',
    [2] = 'diplomat',
    [3] = 'admiral',
    [4] = 'merchant',
    [5] = 'priest',
    [6] = 'general',
    [7] = 'named_character',
    [8] = 'princess',
    [9] = 'heretic',
    [10] = 'witch',
    [11] = 'inquisitor',
    [13] = 'pope',
}

------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: distance
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---@TODO test this function!!!!!!
---get the direct distance between two points
---@param originX integer
---@param originY integer
---@param goalX integer
---@param goalY integer
---@return number
function module.directDistance(originX, originY, goalX, goalY)
    return math.sqrt((goalX - originX)^2 + (goalY - originY)^2)
end

--- wrapper for directDistance, returns distance between two characters
---@param character1 character
---@param character2 character
---@return number distance
function module.distanceBetweenCharacters(character1, character2)
    local distance = module.directDistance(character1.xCoord, character1.yCoord, character2.xCoord, character2.yCoord)
    return distance
end

------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: log formatters
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---Format faction's name (both internal and localized) to be printed in logs
---@param faction factionStruct
---@return string
function module.logFaction(faction)
    return 'faction : '..faction.localizedName..' ('..faction.name..')'
end

---Formats character's name (both internal and localized) to be printed in logs
---@param character characterRecord
---@return string
function module.logCharacter(character)
    local characterType = character.character and module.characterTypeToString[character.character.characterType] or 'OFF-MAP'
    return 'character ('..characterType..'): '..character.localizedDisplayName..' ('..character.fullName..'), '..module.logFaction(character.faction)
end

---@param settlement settlementStruct
---@return string
local function getSettlementType(settlement)
    if settlement.isCastle == 1 then return 'castle'
    else return 'city' end
end

---Formats character's name (both internal and localized) to be printed in logs
---@param settlement settlementStruct
---@return string
function module.logSettlement(settlement)
    return 'settlement ('.. getSettlementType(settlement)..'): '..settlement.localizedName..' ('..settlement.name..'), '..module.logFaction(settlement.ownerFaction)
end

---@param buildLvl buildingLevel
function module.logBuildingLevel(buildLvl)
    local currentFaction = M2TW and M2TW.campaign and M2TW.campaign.currentFaction.factionID or 0
    return 'building level :'..buildLvl:getLocalizedName(currentFaction)..' ('..buildLvl.name..')'
end

------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: character related
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---@TODO test this function
---@param charRec characterRecord
---@param traitList table<string>
---@return boolean
function module.hasOneOfTraits(charRec, traitList)
    local thisTrait = charRec:getTraits()
    local traitSet = module.listToSet(traitList)
    while thisTrait ~= nil do
        if traitSet[thisTrait.name] then return true end
        thisTrait = thisTrait.nextTrait
    end
    return false
    
end


---Print all characters traits, based on m2tweop documentation
---@param namedCharacter characterRecord
function module.printTraits(namedCharacter)
    local thisTrait, traitsList, index = namedCharacter:getTraits(), namedCharacter.fullName.." traits:", 0
    while thisTrait ~= nil do
        traitsList, thisTrait, index = traitsList.."\n\tTrait "..index.." - Name: "..thisTrait.name.." - Level: "..thisTrait.level, thisTrait.nextTrait, index + 1
    end
    print(traitsList)
end


---@param character character
function module.giveRandomTraitsCharacter(character)
    local traits = {}
    if character.characterType == characterType.priest then
        traits = {
            {name = "NaturalPriestSkill", levelMax = 3},
            {name = "StrictlyReligiousPriest", levelMax = 1},
            {name = "Warmonger", levelMax = 3},
            {name = "Corrupted", levelMax = 3},
            {name = "StrongFaith", levelMax = 4},
            }
    elseif character.characterType == characterType.spy then
        traits = {
            {name = "NaturalSpySkill", levelMax = 3},
            {name = "GoodSpy", levelMax = 5},
            {name = "SpyCareer", levelMax = 3},
            {name = "Spy_PirateConnections", levelMax = 1},
            {name = "Spy_AssassinConnections", levelMax = 1},
            {name = "Spy_Middle_EasternConnections", levelMax = 1},
            }
    elseif character.characterType == characterType.assassin then
        traits = {
            {name = "NaturalAssassinSkill", levelMax = 3},
            {name = "GoodAssassin", levelMax = 5},
            {name = "GoodConspirator", levelMax = 3},
            {name = "AssassinCareer", levelMax = 3},
            }
    elseif character.characterType == characterType.named_character then
        -- special case for mongol armies, as they are spawned a lot:
        if character.faction.name == 'mongols' then
            traits = {
                {name = "MilitaryInclination", levelMax = 1},
                {name = "NaturalMilitarySkill", levelMax = 4},
                {name = "GoodCommander", levelMax = 5},
                {name = "Bloodthirsty", levelMax = 2},
                {name = "BattleDread", levelMax = 5},
                {name = "StrategyDread", levelMax = 5},
                {name = "PublicFaith", levelMax = 4},
                {name = "GoodCavalryGeneral", levelMax = 3},
                {name = "Loyal", levelMax = 4},
                }
        else
            traits = {
            {name = "MilitaryInclination", levelMax = 1},
            {name = "NaturalMilitarySkill", levelMax = 4},
            {name = "GoodCommander", levelMax = 5},
            {name = "StrictlyReligiousGeneral", levelMax = 1},
            {name = "GoodInfantryGeneral", levelMax = 3},
            {name = "StrategyChivalry", levelMax = 5},
            {name = "PublicFaith", levelMax = 4},
            {name = "GoodCavalryGeneral", levelMax = 3},
            {name = "Loyal", levelMax = 4},
            }
        end
    else return end
    log.info('Adding random traits to '..module.logCharacter(character.characterRecord))
    for i, trait in ipairs(traits) do
        local level = module.randomNumber(0,traits[i].levelMax)
        if level ~= 0 then character.characterRecord:addTrait(trait.name, level)
            log.info("\t * Character received trait: " .. trait.name .. " level: " .. level)
        end
    end
end

------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: events
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---@param counterName string
---@param amount integer
---@return integer newValue
function module.increaseEventCounter(counterName, amount)
    local oldValue = M2TWEOP.getScriptCounter(counterName)
    local newValue = oldValue + amount
    M2TWEOP.setScriptCounter(counterName, newValue)
    return newValue
end

------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---MARK: profiler
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---sub-table to manage profiler operations
module.profiler = {}
---Stores profiler results to ba shown in GUI
module.profiler.data = {}
---Stores time of last profilerData update
module.profiler.lastTimeChecked = 0
---updates profilerData
function module.profiler.getData()
	if os.time() - module.profiler.lastTimeChecked > 10 then
		module.profiler.lastTimeChecked = os.time()
		module.profiler.data = profile.query(20)
	end
end

return module
