local module = {}

---@class Step
local Step = {}

---@return boolean
function Step.isDone() return false end

---@param facStruct factionStruct
---@return boolean
function Step.conditions(facStruct) return false end

---@param facStruct factionStruct
---@return boolean
function Step.fallbackConditions(facStruct) return false end

---@param facStruct factionStruct
function Step.effects(facStruct) end

---@TODO
function Step:validate()
end

---@TODO
---@param facStruct factionStruct
function Step:trigger(facStruct)
	if self.isDone() then return end
	if self.conditions(facStruct) or self:.allbackConditions(facStruct) then
		self.effects(facStruct)
	end
end

function Step:new()
	local o = {
		nameStr = "",
		contextStr = "",
		requirementsStr = "",
	}
	setmetatable(o, self)
	self.__index = self
	return o
end


---@param counterName string
---@return boolean
local function isCounterPositive(counterName) return M2TWEOP.getScriptCounter(counterName) > 0 end


---@param counterName string
---@return boolean
local function isCounterOne(counterName) return M2TWEOP.getScriptCounter(counterName) == 1 end


local ostsiedlung = Step:new()
ostsiedlung.nameStr = "Ostsiedlung"
ostsiedlung.isDone = function() return isCounterOne('OSTSIEDLUNG') == 1 end
ostsiedlung.conditions = function() return M

	-- 	if I_EventCounter OSTSIEDLUNG < 1							; in case any other Ostsiedlung trigger already has fired
	-- 	and I_TurnNumber > 99
	-- 	and I_CompareCounter number_landowners2_built > 2			; 3+ Local Guards were built (Large Town level: eg Gdansk, Szczecin, Olomoc)
	-- 	and I_CompareCounter number_mines_built > 1					; ATTENTION do not count the firts level of mines on this (openshaft pits) !   2+ Shaft Mines were build (Wroclaw, Krakow: miners were first people to move east)

	-- 		log -------------------- Poland evolution: (2) OSTSIEDLUNG ----------------------------------------
	-- 		; historically should take place around turn 100 - (ca. 1180) - higher growth of the settlements
	-- 		historic_event OSTSIEDLUNG

	-- 		if not I_IsFactionAIControlled poland
	-- 			set_event_counter poland_ostsiedlung_player 1
	-- 		end_if
	-- 		if not I_IsFactionAIControlled hungary					; in case the player is Hungary, but it was Poland AI triggering the event
	-- 			set_event_counter hungary_ostsiedlung_player 1
	-- 		end_if
	-- 		if not I_IsFactionAIControlled hre						; in case the player is HRE, but it was Poland AI triggering the event
	-- 			set_event_counter hre_ostsiedlung_player 1
	-- 		end_if
	-- 	end_if


local firstTourney = Step:new()
firstTourney.nameStr = "First Tourney"

local allEvolutions = {
    poland = {firstTourney,}
}

--[[
; IMPORTANT: faction evolutions must triger be placed before the part that resets counter FL_is_crowned_ruler ---@TODO why?


; Potential poland events to be introduced:
; - chronicles: Kalubek 1220, 
;  - 1368 statut zup solnych - increase of productivity of mines

; consider initial peace:
; and not I_LocalFaction poland
; and not I_LocalFaction hungary
; console_command diplomatic_stance poland hungary peace

															; turn 100-150 ostsiedlung
set_event_counter poland3_first_tourney_player 0			; turn 180-220
set_event_counter poland4_location_of_cracow_player 0		; turn 250-300
set_event_counter poland5_council_of_leczyca_player 0		; turn 300-370
set_event_counter poland6_united_kingdom_player 0			; turn 400-470
set_event_counter poland7_golden_age_player 0				; turn 560-640

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType poland

	set_event_counter poland_supported_due_to_capital_occupation 0

	
		; -------------------- from there every evolution require poland to: own Krakow that is not under siege, be no excommunicated
		if I_SettlementOwner Krakow = poland
		and not I_SettlementUnderSiege Krakow
		and I_EventCounter poland_is_excommunicated == 0

			if not I_EventCounter POLAND3_FIRST_TOURNEY == 1

				log -------------------- Poland evolution: (3) FIRST TOURNEY ----------------------------------------
				; First Tourney 1243, it impacts on the possibility to build Tourney Fields and on recruitment of some knights

				set_event_counter continue 0

				if I_TurnNumber > 180
				and I_CompareCounter number_tourney_fields_built > 2		; Poland has built 3 Tourney Fields (Minor City level)
				and I_EventCounter FL_is_crowned_ruler == 1					; FL has the crown
				and I_SettlementOwner Wroclaw = poland						; first tourney was held in Silesia
					set_event_counter continue 1
				end_if

				if I_TurnNumber > 240										; FALL BACK - in case of failure or a bug
					set_event_counter continue 1
				end_if

				if I_EventCounter continue = 1
					set_event_counter continue 0

					add_events
						event	historic POLAND3_FIRST_TOURNEY factions { poland, hre, france, england, norway, denmark, hungary, lithuania, kievan_rus, russia, cumans, }
						date	0
						position 194, 222
					end_add_events
					if I_EventCounter POLAND3_FIRST_TOURNEY_accepted > 0
						disable_cursor
						disable_shortcuts true
						ui_indicator_remove 0
						ui_indicator_remove 1
						snap_strat_camera 194, 222
						zoom_strat_camera 1.0
						reveal_tile 194, 222				
						campaign_wait 1
						ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
						campaign_wait 4
						ui_indicator_remove 1
						campaign_wait 2
						ui_indicator_remove 0
						enable_cursor
						disable_shortcuts false
					end_if
					if I_EventCounter POLAND3_FIRST_TOURNEY_declined > 0
						ui_indicator_remove 0
						ui_indicator_remove 1
					end_if
					if not I_IsFactionAIControlled poland
						set_event_counter poland3_first_tourney_player 1
					end_if
				end_if
			end_if

			if not I_EventCounter POLAND4_LOCATION_OF_CRACOW == 1
			and I_EventCounter POLAND3_FIRST_TOURNEY = 1

				log -------------------- Poland evolution: (4) LOCATION OF CRACOW ----------------------------------------
				; wave of location of the cities in the wake of Ostsiedlung (1230-1300)
				set_event_counter continue 0

				if I_TurnNumber > 250
				and I_CompareCounter number_city_hall_built > 2				; Poland has built 3 City Halls (Large City level)
					set_event_counter continue 1
				end_if

				if I_TurnNumber > 300										; FALL BACK - in case of failure or a bug
					set_event_counter continue 1
				end_if

				if I_EventCounter continue = 1

					add_events
						event	historic POLAND4_LOCATION_OF_CRACOW factions { poland, hre, france, england, norway, denmark, hungary, lithuania, kievan_rus, russia, cumans, }
						date	0
						position 219, 212
					end_add_events
					if I_EventCounter POLAND4_LOCATION_OF_CRACOW_accepted > 0
						disable_cursor
						disable_shortcuts true
						ui_indicator_remove 0
						ui_indicator_remove 1
						snap_strat_camera 219, 212
						zoom_strat_camera 1.0
						reveal_tile 219, 212
						campaign_wait 1
						ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
						campaign_wait 4
						ui_indicator_remove 1
						campaign_wait 2
						ui_indicator_remove 0
						enable_cursor
						disable_shortcuts false
					end_if
					if I_EventCounter POLAND4_LOCATION_OF_CRACOW_declined > 0
						ui_indicator_remove 0
						ui_indicator_remove 1
					end_if
					if not I_IsFactionAIControlled poland
						set_event_counter poland4_location_of_cracow_player 1
					end_if
					set_event_counter continue 0

				end_if
			end_if

			if not I_EventCounter POLAND5_COUNCIL_OF_LECZYCA == 1
			and I_EventCounter POLAND4_LOCATION_OF_CRACOW = 1
				log -------------------- Poland evolution: (5) COUNCIL OF LECZYCA ----------------------------------------
				; changes in the ecclesiastical life of Poland  (1280-1320)
				set_event_counter continue 0

				if I_TurnNumber > 300
				and I_CompareCounter number_cathedral_built > 3				; Poland has built 4 Cathedrals (Large City level)
					set_event_counter continue 1
				end_if

				if I_TurnNumber > 370										; FALL BACK - in case of failure or a bug
					set_event_counter continue 1
				end_if

				if I_EventCounter continue = 1
				and I_SettlementOwner Plock = poland
				and not I_SettlementUnderSiege Plock

					set_event_counter continue 0
					historic_event POLAND5_COUNCIL_OF_LECZYCA factions { poland, hre, france, england, norway, denmark, hungary, lithuania, kievan_rus, russia, cumans, }
					if not I_IsFactionAIControlled poland
						set_event_counter poland5_council_of_leczyca_player 1
					end_if
				end_if
			end_if

			if not I_EventCounter POLAND6_UNITED_KINGDOM == 1
				log -------------------- Poland evolution: (6) UNITED KINGDOM ----------------------------------------
				; reunification of Poland under Wladislav the Elbow (1300-1330)
				set_event_counter continue 0

				if I_SettlementOwner Poznan = poland 		; Wladislav united 3 provices (Silesia and Pomerania not required)
				and I_SettlementOwner Plock = poland

					if I_TurnNumber > 400
					and I_CompareCounter number_citadel_built > 2				; Poland has built 3 Citadels (Large City castle level) (as player)
					and I_EventCounter FL_is_crowned_ruler == 1					; FL has the crown
						set_event_counter continue 1
					end_if

					if I_TurnNumber > 470										; FALL BACK - in case of failure or a bug
						set_event_counter continue 1
					end_if

					if I_EventCounter continue = 1
						set_event_counter continue 0
						historic_event POLAND6_UNITED_KINGDOM factions { poland, hre, france, england, norway, denmark, hungary, lithuania, kievan_rus, russia, cumans, }
						if not I_IsFactionAIControlled poland
							set_event_counter poland6_united_kingdom_player 1
						end_if
					end_if
				end_if
			end_if


			if I_EventCounter POLAND7_GOLDEN_AGE == 0
				log -------------------- Poland evolution: (7) GOLDEN AGE ----------------------------------------
				; Golden Age of 15th century (1450-1600)
				set_event_counter continue 0

				if I_TurnNumber > 560
				and I_CompareCounter number_great_university_built > 0		; Great University exists (Huge City level)
				and I_CompareCounter number_cathedral_built > 4				; Poland has built 5 Cathedrals (Large City level)
				and I_EventCounter FL_is_crowned_ruler == 1					; FL has the crown
					set_event_counter continue 1
				end_if

				if I_TurnNumber > 640										; FALL BACK - in case of failure or a bug
					set_event_counter continue 1
				end_if

				if I_EventCounter continue = 1
					set_event_counter continue 0
					historic_event POLAND7_GOLDEN_AGE factions { poland, hre, france, england, norway, denmark, hungary, lithuania, kievan_rus, russia, cumans, }
					if not I_IsFactionAIControlled poland
						set_event_counter poland7_golden_age_player 1
					end_if
					terminate_monitor ; this is the final evolution, so we can terminate the whole script once it's achieved
				end_if
			end_if
		end_if
	end_if


		;---------------------------------------------- events --------------------------------------
	if RandomPercent < 30

		if not I_EventCounter POLAND20_ORDER_OF_DOBRIN == 1
		and I_EventCounter OSTSIEDLUNG == 1
		and I_EventCounter poland_is_excommunicated == 0

			log -------------------- Poland event: (A) ORDER OF DOBRIN ----------------------------------------
			set_event_counter continue 0

			if I_TurnNumber > 100										; redundant
		;     and I_CompareCounter number_chapter_houses_built > 0		; Poland owns a chapter house - include this condition when the relevant counter exists
			and I_SettlementOwner Twangste = slave						; Pagans are still in Prussia (allows setting up of hte order earlier)
			and I_SettlementOwner Plock = poland						; Poland is accross the border
			and not I_SettlementUnderSiege Plock
				set_event_counter continue 1
			end_if

			if I_TurnNumber > 200										; redundant
		;     and I_CompareCounter number_chapter_houses_built > 0		; Poland owns a chapter house - include this condition when the relevant counter exists
			and I_SettlementOwner Plock = poland						; Poland is accross the border
			and not I_SettlementUnderSiege Plock
				set_event_counter continue 1
			end_if
			
			if I_EventCounter continue = 1
				set_event_counter continue 0
				add_events
					event	historic POLAND20_ORDER_OF_DOBRIN factions { poland, hre, denmark, lithuania, kievan_rus, russia, }
					date	0
					position 226, 224
				end_add_events
				if I_EventCounter POLAND20_ORDER_OF_DOBRIN_accepted > 0
					disable_cursor
					disable_shortcuts true
					ui_indicator_remove 0
					ui_indicator_remove 1
					snap_strat_camera 226, 224
					zoom_strat_camera 1.0
					reveal_tile 226, 224			
					campaign_wait 1
					ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
					campaign_wait 4
					ui_indicator_remove 1
					campaign_wait 2
					ui_indicator_remove 0
					enable_cursor
					disable_shortcuts false
				end_if
				if I_EventCounter POLAND20_ORDER_OF_DOBRIN_declined > 0
					ui_indicator_remove 0
					ui_indicator_remove 1
				end_if
				spawn_army															; when the military orders are re-coded, change it to avoid Teutonic Knights before they are
					faction poland
					character	Bernard, general, age 32, x 226, y 224
					unit	Ritterbruder				exp 0 armour 0 weapon_lvl 0
					unit	Dismounted Ritterbruder		exp 0 armour 0 weapon_lvl 0
					unit	Crusader Sergeants			exp 0 armour 0 weapon_lvl 0
					unit	Crusader Sergeants			exp 0 armour 0 weapon_lvl 0
				end
				if not I_IsFactionAIControlled poland
					set_event_counter poland20_order_of_dobrin_player 1
				end_if
			end_if
		end_if

	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;========================================================================================================================
;====================================================================================================== HUNGARY EVOLUTION
;---------- Hungary Evolution

; Plans for further work:
; - after invasion of the Mongols and a lost battle there should be the transition from wooden Var into stone Var - for now it's tied to Golden_Bull
; - Cumans event should be also be included in this mechanism
; - Hospitallers should be tied to a crusade (or rather arrival to the Holy Land)

set_event_counter hungary1a_nobility_var_buildable 0
set_event_counter hungary2_saxons_player 0				; turn 10
set_event_counter hungary2a_saxons_declined_counter	0
set_event_counter hungary3_szekely_player 0				; turn 40:
set_event_counter hungary4_hospitallers_arrive_player 0	; turn 60-65:
														; turn 100-150 ostsiedlung
set_event_counter hungary5_golden_bull_player 0			; turn 190:  Golden Bull of Andrew II
set_event_counter hungary6_diploma_andreanum_player 0	; turn 190:
set_event_counter hungary7_cumans_player 0				; turn 200-250: Cumans available in Alfold


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType hungary

log -------------------- Hungary: counters set up at the beginnig of the turn --------------------------------
	
	set_event_counter hungary_supported_due_to_capital_occupation 0


log -------------------- Hungary stability : (A) Financial support after a LOSS OF THE CAPITAL ---------------
	
	if not I_SettlementOwner Esztergom = hungary
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements hungary > 0
	and I_NumberOfSettlements hungary < 8
	
		log -------------------- hungary lacking capital - nobles' financial help
		console_command add_money hungary, 5000
		set_event_counter hungary_supported_due_to_capital_occupation 1

		if not I_IsFactionAIControlled hungary
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

log -------------------- Hungary stability : (B) Support if the FACTION IS SMALL -------------------------------

	; Only for the AI. Big support for 1-3 settlements, smaller for 3-9.
	; Helps to avoid the usual inability of the AI to defend if attacked from multiple directions.

	if I_EventCounter faction_turn_hungary == 1
	and I_IsFactionAIControlled hungary
	and I_EventCounter hungary_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Hungary supported when the player is NOT HUNGARY
	
		if I_EventCounter faction_size_hungary_small == 1
			console_command add_money hungary, 5000
		end_if
		if I_EventCounter faction_size_hungary_medium == 1
			console_command add_money hungary, 2000
		end_if
	end_if
	
	if I_CompareCounter pl_ec_id == 13 ; hre
	and I_EventCounter hungary_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Hungary supported when the player is HRE (add-on)
		
		if I_EventCounter faction_size_hungary_small == 1
			console_command add_money hungary, 3000
		end_if
		if I_EventCounter faction_size_hungary_medium == 1
			console_command add_money hungary, 1000
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 17 ; byzantium
	and I_EventCounter hungary_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Hungary supported when the player is BYZANTIUM (add-on)
		
		if I_EventCounter faction_size_hungary_small == 1
			console_command add_money hungary, 3000
		end_if
		if I_EventCounter faction_size_hungary_medium == 1
			console_command add_money hungary, 1000
		end_if
	end_if
	

log -------------------- Hungary evolution: (0 1) INFORMATION ------------------------------------

	if not I_IsFactionAIControlled hungary						; only for the player
	and I_TurnNumber < 50 
		if I_TurnNumber == 0
			historic_event HUNGARY0_STARTING_INFO 
		end_if
		if I_TurnNumber == 3
			historic_event HUNGARY1_REFORMS_INFO
		end_if
		if I_TurnNumber == 31
		and I_SettlementOwner Esztergom = hungary
			historic_event HUNGARY32_EGER
		end_if				
		if I_TurnNumber == 41
		and I_SettlementOwner Szekesfehervar = hungary
			historic_event HUNGARY31_SZEKESFEHERVAR
		end_if		
	end_if


log -------------------- Hungary evolution: (1A) NOBILITY ------------------------------------- allows Var building

	if not I_EventCounter HUNGARY1A_NOBILITY == 1
	and I_TurnNumber > 5
		historic_event HUNGARY1A_NOBILITY factions { hre, poland, hungary, cumans, kievan_rus, byzantium, serbia, }
		set_event_counter hungary1a_nobility_var_buildable 1
	end_if


log -------------------- Hungary evolution: (2) SAXONS ----------------------------------------
		
	if I_EventCounter HUNGARY2B_SAXONS_ARRIVE < 1
	and I_EventCounter hungary_is_excommunicated == 0					; Saxons are very pious
	and not I_CrusadeInProgress											; Christian world is not preoccupied with something different
	and I_SettlementOwner Gyulafehervar = hungary						; Hungary holds the province
	and I_SettlementOwner Szekesfehervar = hungary
	and I_TurnNumber > 10
	and RandomPercent < 20

		if I_EventCounter is_the_ai == 1								; for the AI: turn 10
			historic_event HUNGARY2B_SAXONS_ARRIVE factions { hre, poland, hungary, cumans, kievan_rus, byzantium, serbia, }
		end_if

		if I_TurnNumber > 50											; fall-back option: after 50 turns they come without invitation
			historic_event HUNGARY2B_SAXONS_ARRIVE
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary2_saxons_player 1
			end_if
		end_if	
			
		if I_EventCounter hungary2a_saxons_declined_counter > 0
			inc_event_counter hungary2a_saxons_declined_counter -1
		end_if
		
		if I_EventCounter is_the_player == 1
		and I_EventCounter hungary2a_saxons_declined_counter == 0
			
			historic_event HUNGARY2A_SAXONS_INVITE true

			while I_EventCounter HUNGARY2A_SAXONS_INVITE_accepted = 0			; suspend script until decision is made
				and I_EventCounter HUNGARY2A_SAXONS_INVITE_declined = 0
			end_while

			if I_EventCounter HUNGARY2A_SAXONS_INVITE_accepted = 1
				console_command add_money -4000
				historic_event HUNGARY2B_SAXONS_ARRIVE
				set_event_counter hungary2_saxons_player 1
			end_if

			if I_EventCounter HUNGARY2A_SAXONS_INVITE_declined = 1
				set_event_counter HUNGARY2A_SAXONS_INVITE_accepted 0
				set_event_counter HUNGARY2A_SAXONS_INVITE_declined 0
				set_event_counter hungary2a_saxons_declined_counter 5
			end_if			

		end_if

	end_if


log -------------------- Hungary evolution: (3) SZEKELY ----------------------------------------

	if not I_EventCounter HUNGARY3_SZEKELY == 1
	and I_SettlementOwner Szekesfehervar = hungary
	and I_SettlementOwner Varad = hungary
	and I_SettlementOwner Gyulafehervar = hungary
	and not I_SettlementUnderSiege Gyulafehervar
	and not I_CrusadeInProgress
	
		if I_CompareCounter number_of_battles_not_autoresolved_large > 9
			historic_event HUNGARY3_SZEKELY
			inc_recruit_pool Gyulafehervar_Province 2 Magyar Cavalry	
			spawn_army
				faction hungary
					character	Vajk Orsur, named character, age 32, x 249, y 184
					traits LoyaltyStarter 1 , ReligionStarter 1 , Intelligent 7 , MilitaryInclination 1 , Pagan_Edu 3 , NaturalMilitarySkill 1 , GoodCommander 1 , StrategyDread 2
						unit	Magyar Cavalry				exp 4 armour 0 weapon_lvl 0
						unit	Magyar Cavalry				exp 1 armour 0 weapon_lvl 0
						unit	Magyar Cavalry				exp 0 armour 0 weapon_lvl 0
			end				
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary3_szekely_player 1
			end_if
		end_if

		if I_TurnNumber > 100						; fall back
			historic_event HUNGARY3_SZEKELY factions { hre, poland, hungary, cumans, kievan_rus, byzantium, serbia, }
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary3_szekely_player 1
			end_if
		end_if

	end_if


log -------------------- HUNGARY evolution: (4) HOSPITALLERS ARRIVE ----------------------------------------

	if I_EventCounter HUNGARY4_HOSPITALLERS_ARRIVE < 1					; this event spawns a small army for HU faction, one time during the game
	and RandomPercent < 20
	and I_TurnNumber > 60
	and I_SettlementOwner Szekesfehervar = hungary
	and not I_SettlementUnderSiege Szekesfehervar
	and I_EventCounter hungary_is_excommunicated == 0					; and is a good Catholic faction
	and not I_CrusadeInProgress											; Christian world is not preoccupied with something different
	
		if I_EventCounter hospitallers_recruitment_enabled > 0						; the Order has already formed
		and I_EventCounter hospitallers_ready_to_arrive_to_hungary == 0
			add_events
				event	counter	hospitallers_ready_to_arrive_to_hungary				; can be re-made 
				date	30
			end_add_events
		end_if

		if I_EventCounter hospitallers_ready_to_arrive_to_hungary == 1
			add_events
				event	historic HUNGARY4_HOSPITALLERS_ARRIVE factions { hre, poland, hungary, cumans, kievan_rus, byzantium, serbia, }
				date	0
				position 209, 184
			end_add_events
			if I_EventCounter HUNGARY4_HOSPITALLERS_ARRIVE_accepted > 0
				disable_cursor
				disable_shortcuts true
				ui_indicator_remove 0
				ui_indicator_remove 1
				snap_strat_camera 209, 184
				zoom_strat_camera 1.0
				reveal_tile 209, 184			
				campaign_wait 1
				ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
				campaign_wait 4
				ui_indicator_remove 1
				campaign_wait 2
				ui_indicator_remove 0
				enable_cursor
				disable_shortcuts false
			end_if
			if I_EventCounter HUNGARY4_HOSPITALLERS_ARRIVE_declined > 0
				ui_indicator_remove 0
				ui_indicator_remove 1
			end_if
				
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary4_hospitallers_arrive_player 1			; no use yet, may be in the future coding
			end_if
			spawn_army
				faction hungary
					character	Betlehem Tibold, named character, age 32, x 209, y 184
					traits LoyaltyStarter 2 , ReligionStarter 2 , Intelligent 7 , MilitaryInclination 1 , Military_Edu 2 , NaturalMilitarySkill 1 , GoodCommander 1 , StrategyDread 2 , Poet 1
						unit	Knights Hospitaller				exp 0 armour 0 weapon_lvl 0
						unit	Dismounted Knights Hospitaller	exp 0 armour 0 weapon_lvl 0
						unit	Crusader Sergeants				exp 0 armour 0 weapon_lvl 0
						unit	Crusader Sergeants				exp 0 armour 0 weapon_lvl 0
			end
			
		end_if
	end_if


log -------------------- Hungary evolution: (5) GOLDEN BULL ----------------------------------------

	if I_EventCounter HUNGARY5_GOLDEN_BULL < 1
	and RandomPercent < 20
	and I_TurnNumber > 180
	and I_SettlementOwner Szekesfehervar = hungary
	and not I_SettlementUnderSiege Szekesfehervar
	and I_EventCounter hungary_is_excommunicated == 0					; and is a good Catholic faction

		set_event_counter continue 0

		if I_EventCounter SAMARKAND_SACKED > 0							; the Mongols are on the horizon - this should be replaced
			set_event_counter continue 1
		end_if

		if I_EventCounter continue = 1
			set_event_counter continue 0
			add_events
				event	historic HUNGARY5_GOLDEN_BULL factions { hre, france, england, denmark, poland, hungary, cumans, kievan_rus, byzantium, serbia, venice, sicily, }
				date	0
				position 216, 188										; Buda		
			end_add_events
			if I_EventCounter HUNGARY5_GOLDEN_BULL_accepted > 0
				disable_cursor
				disable_shortcuts true
				ui_indicator_remove 0
				ui_indicator_remove 1
				snap_strat_camera 216, 188
				zoom_strat_camera 1.0
				reveal_tile 216, 188			
				campaign_wait 1
				ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
				campaign_wait 4
				ui_indicator_remove 1
				campaign_wait 2
				ui_indicator_remove 0
				enable_cursor
				disable_shortcuts false
			end_if
			if I_EventCounter HUNGARY5_GOLDEN_BULL_declined > 0
				ui_indicator_remove 0
				ui_indicator_remove 1
			end_if
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary5_golden_bull_player 1			; no use yet, may be in the future coding
			end_if
		end_if
	end_if
		

log -------------------- Hungary evolution: (6) DIPLOMA ANDREANUM ----------------------------------------

	if I_EventCounter HUNGARY6_DIPLOMA_ANDREANUM < 1
	and RandomPercent < 20
	and I_TurnNumber > 180
	and I_SettlementOwner Gyulafehervar = hungary	
	and I_SettlementOwner Szekesfehervar = hungary
	and not I_SettlementUnderSiege Szekesfehervar
	and I_EventCounter hungary_is_excommunicated == 0					; and is a good Catholic faction
			
		set_event_counter continue 0

		if I_EventCounter mongols_invasion > 0							; the Mongols are on the horizon - this should be replaced in coding in the future
			set_event_counter continue 1
		end_if

		if I_EventCounter continue = 1
			set_event_counter continue 0
			add_events
				event	historic HUNGARY6_DIPLOMA_ANDREANUM factions { hre, poland, hungary, cumans, kievan_rus, byzantium, serbia, }
				date	0
				position 248, 180										; Brasov/Gyualfehervar
			end_add_events
			if I_EventCounter HUNGARY6_DIPLOMA_ANDREANUM_accepted > 0
				disable_cursor
				disable_shortcuts true
				ui_indicator_remove 0
				ui_indicator_remove 1
				snap_strat_camera 248, 180
				zoom_strat_camera 1.0
				reveal_tile 248, 180			
				campaign_wait 1
				ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
				campaign_wait 4
				ui_indicator_remove 1
				campaign_wait 2
				ui_indicator_remove 0
				enable_cursor
				disable_shortcuts false
			end_if
			if I_EventCounter HUNGARY6_DIPLOMA_ANDREANUM_declined > 0
				ui_indicator_remove 0
				ui_indicator_remove 1
			end_if
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary6_diploma_andreanum_player 1			; no use yet, may be in the future coding
			end_if
		end_if
	end_if


log -------------------- Hungary evolution: (7) CUMANS ----------------------------------------


	if not I_EventCounter HUNGARY7_CUMANS == 1
	and I_EventCounter mongols_invasion > 0			; turn 170

		if I_SettlementOwner Varad = hungary
		and I_SettlementOwner Iasi = mongols
		and I_SettlementOwner Azaq = mongols
		and I_SettlementOwner Sharukan = mongols
		and I_NumberOfSettlements cumans < 4
		and RandomPercent < 20			
			historic_event HUNGARY7_CUMANS factions { hre, poland, hungary, cumans, kievan_rus, byzantium, serbia, }
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary7_cumans_player 1
			end_if
			spawn_army
				faction hungary
				character	Mog az_Oguz, named character, age 42, x 227, y 181			; location in the middle of Puszta, traits were well thought-out
					traits LoyaltyStarter 3 , ReligionStarter 1 , Intelligent 7 , MilitaryInclination 1 , Pagan_Edu 2 , NaturalMilitarySkill 1 , HatesMongols 1 , GoodCommander 1 , Drink 2 , StrategyDread 2 , LeaderGodNotMine 1 , Loyal 1 , ContentGeneral 3
						unit	Cuman Horse Militia				exp 4 armour 0 weapon_lvl 0
						unit	Cuman Horse Militia				exp 4 armour 0 weapon_lvl 0
						unit	Cuman Horse Militia				exp 1 armour 0 weapon_lvl 0
						unit	Cuman Horse Militia				exp 0 armour 0 weapon_lvl 0
			end				
		end_if
		
		if I_TurnNumber > 260												; fall-back
			historic_event HUNGARY7_CUMANS factions { papal_states, }		; to make it not visible to the player
			if not I_IsFactionAIControlled hungary
				set_event_counter hungary7_cumans_player 1
			end_if
		end_if

	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;==========================================================================================================================
;====================================================================================================== JERUSALEM EVOLUTION
;---------- Kingdom of Jerusalem Evolution
; KoJ gets a lot of money through interactive event - you need to analyse it to mod events here
; in EBII mod there's a mechanism for Baktria related to large battles - should be employed here

;===================== HATTIN EVENT ========================
; 098: description and pics have been adjusted.
; important also in recruitment, eg. in the Norman Stronghold buildings

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType jerusalem

	if not I_IsFactionAIControlled jerusalem
		log -------------------- JERUSALEM evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event JERUSALEM0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event JERUSALEM1_REFORMS_INFO
		end_if
	end_if

	;-------------------- Kingdom of Jerusalem stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
		
	if not I_SettlementOwner Jerusalem = jerusalem
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements jerusalem > 0
	and I_NumberOfSettlements jerusalem < 8
		
		log -------------------- Kingdom of Jerusalem lacking capital - nobles' financial help
		console_command add_money jerusalem, 5000

		if not I_IsFactionAIControlled jerusalem
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
		
		log -------------------- JERUSALEM evolution: (XXX) HATTIN ----------------------------------------
		
	if I_NumberOfSettlements jerusalem < 5
	and I_EventCounter BATTLE_HATTIN < 1
	and RandomPercent < 50
		
		log --- Hattin happens : increasing jerusalem king's purse
		historic_event BATTLE_HATTIN
		set_event_counter battle_hattin_purse_increased 1
		increment_kings_purse jerusalem 5000
		if I_SettlementOwner Acre = jerusalem
			inc_recruit_pool Acre_Province 2 Knights Templar
			inc_recruit_pool Acre_Province 2 Dismounted Templars
			inc_recruit_pool Acre_Province 3 Templar Sergeant
		end_if
	
	end_if

	if I_NumberOfSettlements jerusalem > 7
	and I_EventCounter battle_hattin_purse_increased > 0
	and RandomPercent < 50
		
		log --- Hattin recovered : decreasing jerusalem king's purse
		increment_kings_purse jerusalem -5000
		set_event_counter battle_hattin_purse_increased 0
	end_if

	if I_TurnNumber > 140
	and I_EventCounter BATTLE_HATTIN < 1
	and RandomPercent < 50
		
		log --- Hattin fallback : fire the event to have other scripted issues / EDB 
		set_event_counter BATTLE_HATTIN 1
	end_if
	
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;====================================================================================================== FRANCE EVOLUTION
;---------- France Evolution

set_event_counter notre_dame 0						; turn 45-200:  Notre Dame
set_event_counter france3_Albigensian_Crusade_player 0		; turn 150-220:  (already in the code: no additional unrest!)
set_event_counter france4_Notre_Dame_Splendid_player 0		; turn 400: after whole Notre Dame built
set_event_counter france5_x 0						; turn 500:
set_event_counter france6_x 0						; turn 600

; Idea: 1266 first golden coin (ecu) struct in France - make it conditional on the number of markets built by France.


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType france
	; infos triggers will go here

		;-------------------- France stability : (A) Financial support after a LOSS OF THE CAPITAL
		;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
		
		if not I_SettlementOwner Paris = france
		and not I_EventCounter player_size_huge == 1
		and I_NumberOfSettlements france > 0
		and I_NumberOfSettlements france < 8
		
			log -------------------- France lacking capital - nobles' financial help
			console_command add_money france, 5000

			if not I_IsFactionAIControlled france
				historic_event NO_CAPITAL_SUPPORT
			end_if
	
		end_if


	if I_SettlementOwner Paris = france
	and RandomPercent < 40

		if not I_EventCounter ALBIGENSIAN_HERESY == 1
			log -------------------- France evolution: ALBIGENSIANS ----------------------------------------
			set_event_counter continue 0
			
			if I_TurnNumber > 30
			and I_SettlementOwner Poitiers = france							; France is close to Toulouse
			and I_SettlementOwner Clermont = france
				set_event_counter continue 1
				; both conditions and effects to be developed
			end_if

			if I_TurnNumber > 50											; FALL BACK - in case of failure or a bug
				set_event_counter continue 1
			end_if

			if I_EventCounter continue = 1
				set_event_counter continue 0
				add_events
					event	historic ALBIGENSIAN_HERESY factions { france, hre, england, scotland, poland, hungary, aragon, spain, portugal, pisa, venice, }
					date	0
					position 90, 160										; Albi
				end_add_events
				if I_EventCounter ALBIGENSIAN_HERESY_accepted > 0
					disable_cursor
					disable_shortcuts true
					ui_indicator_remove 0
					ui_indicator_remove 1
					snap_strat_camera 90, 160
					zoom_strat_camera 1.0
					reveal_tile 90, 160				
					campaign_wait 1
					ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
					campaign_wait 4
					ui_indicator_remove 1
					campaign_wait 2
					ui_indicator_remove 0
					enable_cursor
					disable_shortcuts false
				end_if
				if I_EventCounter ALBIGENSIAN_HERESY_declined > 0
					ui_indicator_remove 0
					ui_indicator_remove 1
				end_if
				if not I_IsFactionAIControlled france
					inc_event_counter france3_Albigensian_Crusade_player 1
				end_if
			end_if
		end_if

		if not I_EventCounter MONEY_FIRST_ECU == 1								; ECU - mint more productive
		and I_EventCounter ALBIGENSIAN_HERESY = 1
		and I_TurnNumber > 270
			log -------------------- France evolution: ECU ----------------------------------------
			historic_event MONEY_FIRST_ECU factions { france, hre, england, poland, hungary, aragon, spain, portugal, }
		end_if

		if not I_EventCounter FRANCE20_HOPITAL_TONNERRE == 1					; additional health bonus from charity buildings
		and I_SettlementOwner Jerusalem = france
			log -------------------- France evolution: TONNERRE ----------------------------------------
			reveal_tile 112, 190				
			add_events
				event	historic FRANCE20_HOPITAL_TONNERRE factions { france, hre, england, poland, hungary, aragon, spain, portugal, }
				date	0
				position 112, 190												; Tonnerre
			end_add_events
			if I_EventCounter FRANCE20_HOPITAL_TONNERRE_accepted > 0
				disable_cursor
				disable_shortcuts true
				ui_indicator_remove 0
				ui_indicator_remove 1
				snap_strat_camera 112, 190
				zoom_strat_camera 1.0
				reveal_tile 112, 190				
				campaign_wait 1
				ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
				campaign_wait 4
				ui_indicator_remove 1
				campaign_wait 2
				ui_indicator_remove 0
				enable_cursor
				disable_shortcuts false
			end_if
			if I_EventCounter FRANCE20_HOPITAL_TONNERRE_declined > 0
				ui_indicator_remove 0
				ui_indicator_remove 1
			end_if
			hide_all_revealed_tiles
		end_if

		if not I_EventCounter FRANCE21_SUGERIUS == 4							; allows Notre Dome process
		and RandomPercent < 10
			log -------------------- France evolution: SUGERIUS ----------------------------------------
				
			if I_IsFactionAIControlled france
				historic_event FRANCE21_SUGERIUS factions { scotland, england, jerusalem, hre, poland, hungary, aragon, spain, portugal, pisa, venice, sicily, }
			end_if

			if not I_IsFactionAIControlled france
				reveal_tile 93, 195
				add_events
					event	historic FRANCE21_SUGERIUS
					date	0
					position 93, 195												; Ile de France
				end_add_events
				if I_EventCounter FRANCE21_SUGERIUS_accepted > 0
					disable_cursor
					disable_shortcuts true
					ui_indicator_remove 0
					ui_indicator_remove 1
					snap_strat_camera 93, 195
					zoom_strat_camera 1.0
					reveal_tile 93, 195				
					campaign_wait 1
					ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
					campaign_wait 4
					ui_indicator_remove 1
					campaign_wait 2
					ui_indicator_remove 0
					enable_cursor
					disable_shortcuts false
				end_if				
				if I_EventCounter FRANCE21_SUGERIUS_declined > 0
					ui_indicator_remove 0
					ui_indicator_remove 1
				end_if
				hide_all_revealed_tiles
			end_if
			
		end_if
		
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;-------------------------------------------------------------------- NOTRE DAME
;
; do not make script demolishing Notre Dame lvl 2 building it because then it'll be displayed at the end of all buildings

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event FactionTurnStart FactionType slave
	and I_EventCounter FRANCE21_SUGERIUS == 1
	and RandomPercent < 20

	if I_IsFactionAIControlled france			; fall-back: AI doesn't know it should build Notre Dame
		set_event_counter notre_dame 1
	end_if

	historic_event NOTRE_DAME_BUILDABLE			; allows building Notre Dame lvl3 and lvl4
	terminate_monitor

end_monitor

monitor_event BuildingCompleted TrueCondition
and SettlementBuildingFinished = wonder_paris_notre_dame_4_gothic
	historic_event NOTRE_DAME_CONSTRUCTED		;
	set_event_counter notre_dame 1				; essential counter for many buildings
	terminate_monitor
end_monitor

monitor_event BuildingCompleted TrueCondition
and SettlementBuildingFinished = wonder_paris_notre_dame_7_glorious
	historic_event NOTRE_DAME_CONSTRUCTED			;
	set_event_counter france4_Notre_Dame_Splendid 1	; for the moment in one script
	terminate_monitor
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;====================================================================================================================
;====================================================================================================== HRE EVOLUTION
;---------- HRE Evolution

set_event_counter hre2_x 0			; turn 80-140:
set_event_counter hre3_x 0			; turn 150-220:
set_event_counter hre4_x 0				; turn 400
set_event_counter hre5_goldene_bulle_karl4_player 0	; turn 450:
set_event_counter hre6_x 0		; turn 600


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType hre

	if not I_IsFactionAIControlled hre
		log -------------------- HRE evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			inc_recruit_pool Lyon_Province -1 Scouts
			inc_recruit_pool Trier_Province -1 Scouts
			inc_recruit_pool Cologne_Province -1 Scouts
			inc_recruit_pool Speyer_Province -1 Scouts
			inc_recruit_pool Frankfurt_Province -1 Scouts
			inc_recruit_pool Ulm_Province -1 Scouts
			inc_recruit_pool Hamburg_Province -1 Mailed Knights
			inc_recruit_pool Hamburg_Province -1 Dismounted Sword Mailed Knights
		end_if			
		if I_TurnNumber == 0
			historic_event HRE0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event HRE1_REFORMS_INFO
		end_if		
	end_if

	;-------------------- HRE stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
		
	if not I_SettlementOwner Frankfurt = hre
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements hre > 0
	and I_NumberOfSettlements hre < 8
	
		log -------------------- HRE lacking capital - nobles' financial help
		console_command add_money hre, 5000

		if not I_IsFactionAIControlled hre
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
		
		
	if RandomPercent < 40
	; more evolutions will go here

		if not I_EventCounter HRE5_GOLDENE_BULLE_KARL4 == 1
			log -------------------- HRE evolution: (5) GOLDENE BULLE ----------------------------------------

			set_event_counter continue 0

			if I_TurnNumber > 400
			and I_SettlementOwner Prague = hre
			and I_SettlementOwner Frankfurt = hre
			and I_SettlementOwner Trier = hre		; historically adopted in Metz
				set_event_counter continue 1
			end_if

			if I_TurnNumber > 450					; FALL BACK - in case of failure or a bug
				set_event_counter continue 1
			end_if

			if I_EventCounter continue = 1
				set_event_counter continue 0
				historic_event HRE5_GOLDENE_BULLE_KARL4 factions { hre, france, england, scotland, norway, poland, hungary, aragon, spain, portugal, pisa, venice, }
				if not I_IsFactionAIControlled hre
					inc_event_counter hre5_goldene_bulle_karl4_player 1
				end_if
			end_if

		end_if

		if not I_EventCounter HRE21_HILDEGARD_OF_BINGEN == 1
			log -------------------- HRE evolution: (21) Hildegard of Bingen ----------------------------------------
			set_event_counter continue 0
			if I_TurnNumber > 22
				set_event_counter continue 1
			end_if
			if I_EventCounter continue = 1
				set_event_counter continue 0
				historic_event HRE21_HILDEGARD_OF_BINGEN factions { hre, france, england, scotland, norway, denmark, poland, hungary, aragon, spain, portugal, pisa, venice, }
				if not I_IsFactionAIControlled hre
					inc_event_counter hre21_hildegard_of_bingen_player 1
				end_if
			end_if
		end_if

	end_if
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;========================================================================================================================
;====================================================================================================== ENGLAND EVOLUTION
;---------- England Evolution

set_event_counter england2_thomas_becket_player 0			; turn 80: Thomas Beckett 
set_event_counter england3_magna_carta_player 0				; turn 160: Magna Carta
set_event_counter england4_mappa_mundi_hereford_player 0	; turn 200
set_event_counter england5_english_archers_player 0			; turn 300:
set_event_counter england6_x_player 0						; turn 

set_event_counter Irish_uprising_near_Dublin 0
	
; one may use here event BOOK_WINCHESTER_BIBILE, SCIENCE_BOOK_ALCHEMY


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType england

	;-------------------- England stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner London = england
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements england > 0
	and I_NumberOfSettlements england < 8
	
		log -------------------- England lacking capital - nobles' financial help
		console_command add_money england, 5000

		if not I_IsFactionAIControlled england
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

	;-------------------- England stability : (B) Support if the FACTION IS SMALL ------------
	; only for the AI
	; big support for 1-3 settlements, smaller for 3-9
	; helps to avoid the usual inability of the AI to run a faction consisting of 2 pieces accross the sea

	if I_EventCounter faction_turn_england == 1
	and I_IsFactionAIControlled england
	and I_EventCounter england_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : England supported when the player is NOT ENGLAND
	
		if I_EventCounter faction_size_england_small == 1
			console_command add_money england, 5000
		end_if
		if I_EventCounter faction_size_england_medium == 1
			console_command add_money england, 2000
		end_if
	end_if
	
	if I_CompareCounter pl_ec_id == 8 ; scotland
	and I_EventCounter england_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : England supported when the player is SCOTLAND (add-on)
		
		if I_EventCounter faction_size_england_small == 1
			console_command add_money england, 3000
		end_if
		if I_EventCounter faction_size_england_medium == 1
			console_command add_money england, 1000
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 12 ; france
	and I_EventCounter england_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : England supported when the player is FRANCE (add-on)
		
		if I_EventCounter faction_size_england_small == 1
			console_command add_money england, 3000
		end_if
		if I_EventCounter faction_size_england_medium == 1
			console_command add_money england, 1000
		end_if
	end_if

	if not I_IsFactionAIControlled england
		log -------------------- ENGLAND evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			inc_recruit_pool York_Province -1 Scouts
			inc_recruit_pool Norwich_Province -1 Scouts
			inc_recruit_pool London_Province -1 Scouts
			inc_recruit_pool Lincoln_Province -1 Scouts
			inc_recruit_pool Bristol_Province -1 Scouts
			inc_recruit_pool Rouen_Province -1 Scouts
		end_if
		if I_TurnNumber == 0
			historic_event ENGLAND0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event ENGLAND1_REFORMS_INFO
		end_if
	end_if
	

	if RandomPercent < 80

		if not I_EventCounter ENGLAND2_THOMAS_BECKET == 1
			log -------------------- ENGLAND evolution: (2) Beckett ----------------------------------------

			if I_TurnNumber > 70
			and RandomPercent < 50
				historic_event ENGLAND2_THOMAS_BECKET factions { hre, france, england, scotland, norway, denmark, poland, hungary, aragon, spain, portugal, pisa, venice, }
				if not I_IsFactionAIControlled england
					inc_event_counter england2_thomas_becket_player 1
				end_if
			end_if
		end_if

		if not I_EventCounter ENGLAND3_MAGNA_CARTA == 1
			log -------------------- ENGLAND evolution: (3) Magna Carta ----------------------------------------

			if I_TurnNumber > 160
			and RandomPercent < 50
				historic_event ENGLAND3_MAGNA_CARTA factions { hre, france, england, scotland, norway, denmark, poland, hungary, aragon, spain, portugal, pisa, venice, }
				if not I_IsFactionAIControlled england
					inc_event_counter england3_magna_carta_player 1
				end_if
			end_if
		end_if

		if not I_EventCounter ENGLAND4_MAPPA_MUNDI_HEREFORD == 1
			log -------------------- ENGLAND evolution: (4) Mappa Mundi ----------------------------------------

			if I_TurnNumber > 200
			and RandomPercent < 50
				historic_event ENGLAND4_MAPPA_MUNDI_HEREFORD factions { hre, france, england, scotland, norway, denmark, poland, hungary, aragon, spain, portugal, pisa, venice, }
				if not I_IsFactionAIControlled england
					inc_event_counter england4_mappa_mundi_hereford_player 1
				end_if
			end_if
		end_if

		if not I_EventCounter ENGLAND5_ENGLISH_ARCHERS == 1
			log -------------------- ENGLAND evolution: (5) English Archers ----------------------------------------

			if I_TurnNumber > 300
			and RandomPercent < 50
				historic_event ENGLAND5_ENGLISH_ARCHERS factions { hre, france, england, scotland, norway, denmark, poland, hungary, aragon, spain, portugal, pisa, venice, }
				if not I_IsFactionAIControlled england
					inc_event_counter england5_english_archers_player 1
				end_if
			end_if
		end_if

		log -------------------- ENGLAND evolution: (21) IRISH UPRSING			----------------------------------------
		
		if I_EventCounter Irish_uprising_near_Dublin < 1
		and I_CharacterTypeNearTile england named_character, 3, 32, 229		; English army is near Dublin
		and I_NumberOfSettlements scotland < 5								; Scotland is big, no need to make hurdless for England
		and I_NumberOfSettlements scotland > 0								; Scotland exists
		
			set_event_counter Irish_uprising_near_Dublin 1					; only one uprising - in the future one may code for more
			
			spawn_army
				faction scotland
				character Finguine mac_Ruadri, named character, age 34, x 22, y 228 
					traits LoyaltyStarter 1 , ReligionStarter 1 , Intelligent 7 , MilitaryInclination 1 , NaturalMilitarySkill 1 , HatesEngland 1 , GoodAdministrator 2 , GoodCommander 1 , PublicFaith 3 , Upright 1 
						unit	Galloglaich Mercs		exp 4 armour 0 weapon_lvl 0
						unit	Galloglaich Mercs		exp 1 armour 0 weapon_lvl 0
						unit	Viking Raiders			exp 4 armour 0 weapon_lvl 0
						unit	Viking Raiders			exp 0 armour 0 weapon_lvl 0
						unit	Irish Kerns				exp 4 armour 0 weapon_lvl 0
						unit	Irish Kerns				exp 1 armour 0 weapon_lvl 0
						unit	Irish Kerns				exp 0 armour 0 weapon_lvl 0
						unit	Irish Kerns				exp 0 armour 0 weapon_lvl 0
						unit	Hunters					exp 4 armour 0 weapon_lvl 0
						unit	Hunters					exp 0 armour 0 weapon_lvl 0
			end
		end_if

	end_if
	
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=========================================================================================================================
;====================================================================================================== SCOTLAND EVOLUTION
;---------- Scotland Evolution

;-------------------- Scotland : setting initial values of counters at the beginning of the game
set_event_counter scotland2_davidian_revolution_player 0				; turn 80: Dabid transformation
set_event_counter scotland3_x 0				; turn 160:
set_event_counter scotland4_x 0				; turn 400
set_event_counter scotland5_x 0				; turn 500: first use of cannons at the beginning of 14th century
set_event_counter scotland6_x 0				; turn 600

set_event_counter scotland_initial_support_choice_made 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType scotland

	;-------------------- Scotland : setting initial values of counters at the beginning of each turn
	set_event_counter scotland_supported_due_to_capital_occupation 0

	
	;-------------------- Scotland stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Edinburgh = scotland
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements scotland > 0
	and I_NumberOfSettlements scotland < 8
	
		log -------------------- Scotland lacking capital - nobles' financial help --------------------------------
		console_command add_money scotland, 5000
		set_event_counter scotland_supported_due_to_capital_occupation 1
		
		if not I_IsFactionAIControlled scotland
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

	;-------------------- Scotland stability : (B) Support if the FACTION IS SMALL ------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-2 settlements, smaller for 3-4
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)


	if I_CompareCounter pl_ec_id == 8 ; scotland

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter scotland_initial_support_choice_accepted == 0
		and I_EventCounter scotland_initial_support_choice_declined == 0
			log --- script start: Player decides if weak scotland should be supported ------------
			historic_event scotland_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter scotland_initial_support_choice_accepted == 0				; suspend script until decision is made
				and I_EventCounter scotland_initial_support_choice_declined == 0
				end_while
			end_if
		end_if

		; give money to player: if player accepted financial support, scotland has not already been supported (due to capital loss), scotland has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter scotland_initial_support_choice_accepted == 1
		and I_EventCounter scotland_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements scotland < 5
		and I_TurnNumber > 0
			log --- script start : scotland supported when the player is scotland ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements scotland > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money scotland, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event scotland_initial_support
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 14 ; england
	and I_EventCounter scotland_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Scotland supported when the player is ENGLAND
		
		if I_EventCounter faction_size_scotland_small == 1
			console_command add_money scotland, 5000
		end_if
		if I_EventCounter faction_size_scotland_medium == 1
			console_command add_money scotland, 3000
		end_if
	end_if

	
	if not I_IsFactionAIControlled scotland
		log -------------------- SCOTLAND evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event SCOTLAND0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event SCOTLAND1_REFORMS_INFO
		end_if
	end_if


	if RandomPercent < 40

		if not I_EventCounter SCOTLAND2_DAVIDIAN_REVOLUTION == 1
			log -------------------- SCOTLAND evolution: (2) DAVIDIAN REVOLUTION ----------------------------------------

			set_event_counter continue 0

			if I_TurnNumber > 30								; one may add here the Rock building
			and I_SettlementOwner Edinburgh = scotland
				set_event_counter continue 1
			end_if

			if I_TurnNumber > 50								; FALL BACK - in case of failure or a bug
				set_event_counter continue 1
			end_if

			if I_EventCounter continue = 1
				set_event_counter continue 0
				historic_event SCOTLAND2_DAVIDIAN_REVOLUTION factions { hre, france, england, scotland, norway, }
				if not I_IsFactionAIControlled scotland
					inc_event_counter scotland2_davidian_revolution_player 1
				end_if
			end_if

		end_if
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;====================================================================================================== NORWAY EVOLUTION
;---------- Norway Evolution
; mind that I_TurnNumber fires in t+1

;-------------------- Norway : setting initial values of counters at the beginning of the game

set_event_counter norway2_konungahella_player 0	; turn 6:
set_event_counter norway3_x 0					; turn 150-220:
set_event_counter norway4_x 0					; turn 400:
set_event_counter norway5_x 0					; turn 500:
set_event_counter norway6_x 0					; turn 600

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType norway

	;-------------------- Norway : setting initial values of counters at the beginning of each turn
	set_event_counter norway_supported_due_to_capital_occupation 0


	;-------------------- Norway stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Bergen = norway
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements norway > 0
	and I_NumberOfSettlements norway < 8
	
		log -------------------- Norway lacking capital - nobles' financial help
		console_command add_money norway, 5000
		set_event_counter norway_supported_due_to_capital_occupation 1
		
		if not I_IsFactionAIControlled norway
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
	;-------------------- Norway stability : (B) Support if the FACTION IS SMALL ------------------------------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-2 settlements, smaller for 3-4
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)

	if I_CompareCounter pl_ec_id == 27 ; norway

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter norway_initial_support_choice_accepted == 0
		and I_EventCounter norway_initial_support_choice_declined == 0
			log --- script start: Player decides if weak Norway should be supported ------------
			historic_event norway_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter norway_initial_support_choice_accepted == 0
				and I_EventCounter norway_initial_support_choice_declined == 0
				end_while
			end_if
		end_if

		; give money to player: if player accepted financial support, norway has not already been supported (due to capital loss), norway has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter norway_initial_support_choice_accepted == 1
		and I_EventCounter norway_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements norway < 5
		and I_TurnNumber > 0
			log --- script start : Norway supported when the player is NORWAY ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements norway > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money norway, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event norway_initial_support
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 6 ;denmark
	and I_EventCounter norway_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Norway supported when the player is DENMARK ------------
		
		if I_EventCounter faction_size_norway_small == 1
			console_command add_money norway, 5000
		end_if
		if I_EventCounter faction_size_norway_medium == 1
			console_command add_money norway, 3000
		end_if
	end_if


	if not I_IsFactionAIControlled norway
	log -------------------- Norway evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event NORWAY0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event NORWAY1_REFORMS_INFO
		end_if
	end_if

	if not I_EventCounter NORWAY2_KONUNGAHELLA == 1
	and I_TurnNumber > 5
	and RandomPercent < 50
	
		log -------------------- Norway evolution: (2) KONUNGAHELLA ----------------------------------------
		;;;;;;;;;; re-mod the building when the one for Scandinavia is ready

		; alternative trigger:
		;		if I_EventCounter NORWAY2_KONUNGAHELLA < 1
		;			and I_CharacterTypeNearTile norway, named_character, 10, 440, 194 ??? check it
		;			historic_event NORWAY2_KONUNGAHELLA
		;		end_if

		historic_event NORWAY2_KONUNGAHELLA factions { poland, hre, england, scotland, norway, denmark, lithuania, russia, }

		console_command create_building Bergen chude2_town

		spawn_army
			faction norway
				character Burislev, named character, age 28, x 178, y 263
				traits CounterOfBattles 5, NaturalMilitarySkill 3 , GoodCommander 1 , Hardened 3 , StrategyDread 2 , Bloodthirsty 1 , Berserker 3 , Paranoia 2
				unit	Chude Militia		exp 1 armour 0 weapon_lvl 0
				unit	Chude Militia		exp 4 armour 0 weapon_lvl 0
				unit	Chude Militia		exp 0 armour 0 weapon_lvl 0
		end

		if not I_IsFactionAIControlled norway
			inc_event_counter norway2_konungahella_player 1
		end_if

	end_if
		
		if not I_IsFactionAIControlled norway
		and I_SettlementOwner Bergen = norway
			log -------------------- Norway evolution: (20) Ivory from Island ----------------------------------------
			set_event_counter continue 0
			inc_event_counter norway20_iceland_tribute_player 1				; they kill walruses and gather ivory
			if I_EventCounter FL_policy_merchant_skills > 0					; the FL is good at asking for moeny
				inc_event_counter norway20_iceland_tribute_player 1
			end_if
			; replace in lua with leader:getTraitLevel('Senile') > 0 
			if I_EventCounter FL_is_senile > 0								; the FL forgets about the North
				inc_event_counter norway20_iceland_tribute_player -1
			end_if
			if I_EventCounter norway20_iceland_tribute_player > 20			; they are able to pay (every 10 years)
			and RandomPercent < 10
				set_event_counter continue 1								; ok, we'll pay
			end_if
			;;; TODO redo in lua ?
			if I_EventCounter FL_player_is_new_this_turn > 0				; they want to pay homage to the new king
				set_event_counter continue 1
			end_if
			if I_EventCounter continue = 1
				set_event_counter continue 0
				historic_event NORWAY20_ICELAND_TRIBUTE factions { poland, hre, england, scotland, norway, denmark, }
				console_command add_money norway, 1000
				if not I_IsFactionAIControlled norway
					set_event_counter norway20_iceland_tribute_player 0
				end_if
			end_if
		end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;========================================================================================================================
;====================================================================================================== DENMARK EVOLUTION
;---------- Denmark Evolution

; once DK events are coded have a look at Twangste rebellion

;-------------------- Denmark : setting initial values of counters at the beginning of the game
set_event_counter denmark2_sankt_erik_player 0	; turn 60-120: St. Erik's Eastern crusade
set_event_counter denmark3_x 0					; turn 150-220:
set_event_counter denmark4_x 0					; turn 400:
set_event_counter denmark5_x 0					; turn 500:
set_event_counter denmark6_x 0					; turn 600: Union of Kalmar ?

set_event_counter denmark_initial_support_choice_made 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType denmark

	;-------------------- Denmark : setting initial values of counters at the beginning of each turn
	set_event_counter Denmark_supported_due_to_capital_occupation 0


	;-------------------- Denmark stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Roskilde = denmark
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements denmark > 0
	and I_NumberOfSettlements denmark < 8
	
		log -------------------- Denmark lacking capital - nobles' financial help
		console_command add_money denmark, 5000

		if not I_IsFactionAIControlled denmark
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if


	;-------------------- Denmark stability : (B) Support if the FACTION IS SMALL ------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-2 settlements, smaller for 3-4
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)

	if I_CompareCounter pl_ec_id == 6 ; denmark

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter denmark_initial_support_choice_accepted == 0
		and I_EventCounter denmark_initial_support_choice_declined == 0
			log --- script start: Player decides if weak denmark should be supported ------------
			historic_event denmark_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter denmark_initial_support_choice_accepted == 0
				and I_EventCounter denmark_initial_support_choice_declined == 0
				end_while
			end_if
			
		end_if

		; give money to player: if player accepted financial support, denmark has not already been supported (due to capital loss), denmark has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter denmark_initial_support_choice_accepted == 1
		and I_EventCounter denmark_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements denmark < 5
		and I_TurnNumber > 0
			log --- script start : denmark supported when the player is denmark ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements denmark > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money denmark, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event denmark_initial_support
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 27 ; norway
	and I_EventCounter denmark_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Denmark supported when the player is NORWAY
		
		if I_EventCounter faction_size_denmark_small == 1
			console_command add_money denmark, 5000
		end_if
		if I_EventCounter faction_size_denmark_medium == 1
			console_command add_money denmark, 2000
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 13 ; HRE
	and I_EventCounter denmark_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Denmark supported when the player is HRE
		
		if I_EventCounter faction_size_denmark_small == 1
			console_command add_money denmark, 5000
		end_if
		if I_EventCounter faction_size_denmark_medium == 1
			console_command add_money denmark, 2000
		end_if
	end_if


	if not I_IsFactionAIControlled denmark
		log -------------------- Denmark evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event DENMARK0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event DENMARK1_REFORMS_INFO
		end_if
	end_if
	
	if not I_EventCounter DENMARK2_SANKT_ERIK == 1
		log -------------------- Denmark evolution: (60+) SANKT ERIK ----------------------------------------
		; St Erik is going on a crusade to Osterland: mythically ca. 1160
		; but the real Danish crusades took place in 13th century
		
		set_event_counter continue 0

		if I_TurnNumber > 60											; triggers for the player
		and not I_IsFactionAIControlled denmark							; to ensure it's not another faction FL who's got the crown
		and I_SettlementOwner Sigtuna = denmark							; Denmark is there
		and not I_SettlementUnderSiege Sigtuna
		and I_SettlementOwner Turku = denmark							; there had been a crusade ;-)
		and I_EventCounter denmark_is_excommunicated == 0
			set_event_counter continue 1
		end_if

		if I_TurnNumber > 110											; fall-back for player, also works if Denmark is AI
		and RandomPercent < 10
			set_event_counter continue 1
		end_if

		if I_EventCounter continue = 1
			set_event_counter continue 0
			spawn_army
				faction denmark
					character	Glum, named character, age 38, x 240, y 280 
					traits CounterOfBattles 10, ReligionStarter 1 , LoyaltyStarter 3 , Handsome 5 , Intelligent 7 , NaturalMilitarySkill 2 , GoodCommander 1 , Hardened 2 , StrategyDread 2 , Berserker 1
						unit	Chude Militia		exp 7 armour 0 weapon_lvl 0
						unit	Chude Militia		exp 1 armour 0 weapon_lvl 0
						unit	Chude Militia		exp 0 armour 0 weapon_lvl 0
			end
			historic_event DENMARK2_SANKT_ERIK factions { denmark, poland, hre, france, england, scotland, norway, hungary, lithuania, kievan_rus, russia, }
			log ------ building St Erik Shrine
			console_command create_building Sigtuna wonder_sigtuna_uppsala_0_reliquary
			wait 0.1
			if not I_IsFactionAIControlled denmark
				set_event_counter denmark2_sankt_erik_player 1			; used also in script for Turku unrest
			end_if
		end_if

	end_if
	
	if not I_EventCounter DENMARK20_DK_COAT_OF_ARMS == 1
	
		log -------------------- DK evolution: (20) Denmark Coat of Arms ----------------------------------------

		set_event_counter continue 0

		if I_TurnNumber > 120
		and I_EventCounter FL_is_crowned_ruler == 1
			set_event_counter continue 1
		end_if

		if I_TurnNumber > 300
			set_event_counter continue 1
		end_if
		
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event DENMARK20_DK_COAT_OF_ARMS factions { hre, france, england, scotland, norway, denmark, poland, hungary, aragon, spain, portugal, pisa, venice, }
			if not I_IsFactionAIControlled denmark
				inc_event_counter denmark20_dk_coat_of_arms_player 1
			end_if
		end_if

	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;=======================================================================================================================
;-------- South-European Factions
;=======================================================================================================================
;=======================================================================================================================

;=======================================================================================================================
;====================================================================================================== Venice EVOLUTION
;---------- Venice Evolution

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType venice
	
log -------------------- Venice evolution: (0 1) INFORMATION ----------------------------------------

	;-------------------- venice stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Venice = venice
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements venice > 0
	and I_NumberOfSettlements venice < 8
	
		log -------------------- venice lacking capital - nobles' financial help
		console_command add_money venice, 5000

		if not I_IsFactionAIControlled venice
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Venice evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;=====================================================================================================================
;====================================================================================================== Pisa EVOLUTION
;---------- Pisa Evolution

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType pisa
	
log -------------------- Pisa evolution: (0 1) INFORMATION ----------------------------------------

	;-------------------- Pisa stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Pisa = pisa
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements pisa > 0
	and I_NumberOfSettlements pisa < 8
	
		log -------------------- Pisa lacking capital - nobles' financial help
		console_command add_money pisa, 5000

		if not I_IsFactionAIControlled pisa
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

log -------------------- Pisa evolution: (3) SACK OF AMALFI ----------------------------------------

	if I_EventCounter PISA02_SACK_OF_AMALFI < 1
	and I_TurnNumber > 5
	and RandomPercent < 20
		historic_event PISA02_SACK_OF_AMALFI factions { pisa, venice, sicily, aragon, spain, portugal, hre, france, england, scotland, byzantium, moors, serbia, }
		set_event_counter pisa_sacked_amalfi 1
	end_if
	
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;====================================================================================================== Sicily EVOLUTION
;---------- Sicily Evolution

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType sicily
	
log -------------------- Sicily evolution: (0 1) INFORMATION ----------------------------------------

	;-------------------- Sicily stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Palermo = sicily
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements sicily > 0
	and I_NumberOfSettlements sicily < 8
	
		log -------------------- Sicily lacking capital - nobles' financial help
		console_command add_money sicily, 5000

		if not I_IsFactionAIControlled sicily
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Sicily evolution: (3) AL-IDRISI ----------------------------------------

; fall-back in turn 105 in case nobody's built the building

	if I_EventCounter AL_IDRISI < 1
	and I_TurnNumber > 100
	and RandomPercent < 20
		historic_event AL_IDRISI factions { pisa, venice, sicily, aragon, spain, portugal, hre, france, england, scotland, byzantium, moors, serbia, }
		set_event_counter al_idrisi_died 1
	end_if
	
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@



;======================================================================================================================	
;====================================================================================================== SPAIN EVOLUTION
;---------- Spain Evolution

set_event_counter spain2_x 0			; turn 80-140:
set_event_counter spain3_x 0			; turn 150-220:
set_event_counter spain4_x 0				; turn 400
set_event_counter spain5_x 0	; turn 500: x
set_event_counter spain6_x 0		; turn 600


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType spain
	
log -------------------- Spain evolution: (0 1) INFORMATION ----------------------------------------


	;-------------------- Spain stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Burgos = spain
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements spain > 0
	and I_NumberOfSettlements spain < 8
	
		log -------------------- Spain lacking capital - nobles' financial help
		console_command add_money spain, 5000

		if not I_IsFactionAIControlled spain
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Spain evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;====================================================================================================== ARAGON EVOLUTION
;---------- Aragon Evolution

set_event_counter aragon2_x 0			; turn 80-140:
set_event_counter aragon3_x 0			; turn 150-220:
set_event_counter aragon4_x 0				; turn 400
set_event_counter aragon5_catalan_atlas_player 0	; turn 500: event is among historical events
set_event_counter aragon6_x 0		; turn 600

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType aragon
	
log -------------------- Aragon evolution: (0 1) INFORMATION ----------------------------------------


	;-------------------- Aragon stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Zaragoza = aragon
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements aragon > 0
	and I_NumberOfSettlements aragon < 8
	
		log -------------------- Aragon lacking capital - nobles' financial help
		console_command add_money aragon, 5000

		if not I_IsFactionAIControlled aragon
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Aragon evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=========================================================================================================================
;====================================================================================================== PORTUGAL EVOLUTION
;---------- Portugal Evolution

;-------------------- Portugal : setting initial values of counters at the beginning of the game
set_event_counter portugal2_x 0			; turn 80-140:
set_event_counter portugal3_x 0			; turn 150-220:
set_event_counter portugal4_x 0				; turn 400
set_event_counter portugal5_asd_player 0	; turn 500:
set_event_counter portugal6_x 0		; turn 600

set_event_counter portugal_initial_support_choice_made 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType Portugal

	;-------------------- Portugal : setting initial values of counters at the beginning of each turn
	set_event_counter portugal_supported_due_to_capital_occupation 0


	;-------------------- Portugal stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Coimbra = portugal
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements portugal > 0
	and I_NumberOfSettlements portugal < 8
	
		log -------------------- Portugal lacking capital - nobles' financial help
		console_command add_money portugal, 5000
		set_event_counter portugal_supported_due_to_capital_occupation 1

		if not I_IsFactionAIControlled portugal
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

	;-------------------- Portugal stability : (B) Support if the FACTION IS SMALL ------------
	; large support for 1-3 settlements, smaller for 4-9, also smaller and random for the player
	; solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; it'd be better if it fires at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries, but it can be here as well


	if I_CompareCounter pl_ec_id == 15 ; portugal

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter portugal_initial_support_choice_accepted == 0
		and I_EventCounter portugal_initial_support_choice_declined == 0
			log --- script start: Player decides if weak portugal should be supported ------------
			historic_event portugal_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter portugal_initial_support_choice_accepted == 0
				and I_EventCounter portugal_initial_support_choice_declined == 0
				end_while
			end_if
			
		end_if

		; give money to player: if player accepted financial support, portugal has not already been supported (due to capital loss), portugal has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter portugal_initial_support_choice_accepted == 1
		and I_EventCounter portugal_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements portugal < 5
		and I_TurnNumber > 0
			log --- script start : portugal supported when the player is portugal ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements portugal > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money portugal, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event portugal_initial_support
		end_if
	end_if

	if I_TurnNumber > 0
	and I_EventCounter portugal_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1

		if I_EventCounter faction_turn_portugal == 1
		and I_IsFactionAIControlled portugal
			log --- script start : Portugal supported when the player is NOT PORTUGAL
			if I_EventCounter faction_size_portugal_small == 1
				console_command add_money portugal, 5000
			end_if
			if I_EventCounter faction_size_portugal_medium == 1
				console_command add_money portugal, 3000
			end_if
		end_if

		if I_CompareCounter pl_ec_id == 18 ; moors
			log --- script start : Portugal supported when the player is the MOORS (add-on)
			if I_EventCounter faction_size_portugal_small == 1
				console_command add_money portugal, 5000
			end_if
			if I_EventCounter faction_size_portugal_medium == 1
				console_command add_money portugal, 3000
			end_if
		end_if
	end_if
	

log -------------------- Portugal evolution: (0 1) INFORMATION ----------------------------------------

log -------------------- Portugal evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;=======================================================================================================================
;-------- Pagan Factions
;=======================================================================================================================
;=======================================================================================================================

;==========================================================================================================================
;====================================================================================================== Lithuania EVOLUTION
;---------- LITHUANIA EVOLUTION

;-------------------- Lithuania : setting initial values of counters at the beginning of the game
set_event_counter lithuania2_x 0						; turn 80:
set_event_counter lithuania3_mindaugas_player 0			; turn 200: Mindaugas
set_event_counter lithuania4_x 0						; turn 400
set_event_counter lithuania5_lithuanian_empire_player 0	; turn 500:
set_event_counter lithuania6_x 0						; turn 600

set_event_counter lithuania_initial_support_choice_made 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType lithuania

	;-------------------- Lithuania : setting initial values of counters at the beginning of each turn
	set_event_counter lithuania_supported_due_to_capital_occupation 0
	
	
	;-------------------- Lithuania stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Kernave = lithuania
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements lithuania > 0
	and I_NumberOfSettlements lithuania < 8
	
		log -------------------- Lithuania lacking capital - nobles' financial help
		console_command add_money lithuania, 5000
		set_event_counter lithuania_supported_due_to_capital_occupation 1
	
		if not I_IsFactionAIControlled lithuania
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

	;-------------------- Lithuania stability : (B) Support if the FACTION IS SMALL ------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-2 settlements, smaller for 3-4
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)

	if I_CompareCounter pl_ec_id == 23 ; lithuania

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter lithuania_initial_support_choice_accepted == 0
		and I_EventCounter lithuania_initial_support_choice_declined == 0
			log --- script start: Player decides if weak lithuania should be supported ------------
			historic_event lithuania_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter lithuania_initial_support_choice_accepted == 0
				and I_EventCounter lithuania_initial_support_choice_declined == 0
				end_while
			end_if
			
		end_if

		; give money to player: if player accepted financial support, lithuania has not already been supported (due to capital loss), lithuania has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter lithuania_initial_support_choice_accepted == 1
		and I_EventCounter lithuania_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements lithuania < 5
		and I_TurnNumber > 0
			log --- script start : lithuania supported when the player is lithuania ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements lithuania > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money lithuania, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event lithuania_initial_support
		end_if
	end_if

	; support for the AI depending on what faction the player controls
	if I_TurnNumber > 0
	and I_EventCounter lithuania_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
		set_event_counter continue 0
		if I_CompareCounter pl_ec_id == 16 ; poland
			log --- script start : Lithuania supported when the player is POLAND
			set_event_counter continue 1
		end_if
		if I_CompareCounter pl_ec_id == 19 ;russia
			log --- script start : Lithuania supported when the player is NOVGOROD
			set_event_counter continue 1
		end_if
		if I_CompareCounter pl_ec_id == 24 ; kievan_rus
			set_event_counter continue 1
			log --- script start : Lithuania supported when the player is KIEVAN RUS
		end_if
		if I_EventCounter continue == 1
			if I_EventCounter faction_size_lithuania_small == 1
				console_command add_money lithuania, 5000
			end_if
			if I_EventCounter faction_size_lithuania_medium == 1
				console_command add_money lithuania, 3000
			end_if
		end_if
		set_event_counter continue 0
	end_if

	if not I_IsFactionAIControlled lithuania
		log -------------------- Lithuania evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event LITHUANIA0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event LITHUANIA1_REFORMS_INFO
		end_if
	end_if
	
		
		if not I_EventCounter LITHUANIA3_MINDAUGAS == 1
		and I_TurnNumber > 200
		and I_SettlementOwner Kernave = lithuania
		and RandomPercent < 20


			log -------------------- Lithuania evolution: (3) MINDAUGAS ----------------------------------------
			reveal_tile 93, 195				
			add_events
				event	historic LITHUANIA3_MINDAUGAS factions { hungary, poland, hre, russia, kievan_rus, cumans, norway, denmark, }
				date	0
				position 249, 240												; Kernave
			end_add_events
			if I_EventCounter LITHUANIA3_MINDAUGAS_accepted > 0
				disable_cursor
				disable_shortcuts true
				ui_indicator_remove 0
				ui_indicator_remove 1
				snap_strat_camera 249, 240
				zoom_strat_camera 1.0
				reveal_tile 249, 240				
				campaign_wait 1
				ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
				campaign_wait 4
				ui_indicator_remove 1
				campaign_wait 2
				ui_indicator_remove 0
				enable_cursor
				disable_shortcuts false
			end_if
			if I_EventCounter LITHUANIA3_MINDAUGAS_declined > 0
				ui_indicator_remove 0
				ui_indicator_remove 1
			end_if				
			if not I_IsFactionAIControlled lithuania
				inc_event_counter lithuania3_mindaugas_player 1
			end_if
			hide_all_revealed_tiles
		end_if

		if not I_EventCounter LITHUANIA5_LITHUANIAN_EMPIRE == 1
		and I_EventCounter LITHUANIA3_MINDAUGAS == 1
		and I_TurnNumber > 400
		and I_SettlementOwner Kernave = lithuania
		and RandomPercent < 20
		
			log -------------------- Lithuania evolution: (5) LITHUANIAN_EMPIRE ----------------------------------------
			add_events
				event	historic LITHUANIA5_LITHUANIAN_EMPIRE factions { hungary, poland, hre, russia, kievan_rus, cumans, norway, denmark, }
				date	0
				position 249, 240												; Kernave
			end_add_events
			if I_EventCounter LITHUANIA5_LITHUANIAN_EMPIRE_accepted > 0
				disable_cursor
				disable_shortcuts true
				ui_indicator_remove 0
				ui_indicator_remove 1
				snap_strat_camera 249, 240
				zoom_strat_camera 1.0
				reveal_tile 249, 240				
				campaign_wait 1
				ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
				campaign_wait 4
				ui_indicator_remove 1
				campaign_wait 2
				ui_indicator_remove 0
				enable_cursor
				disable_shortcuts false
			end_if
			if I_EventCounter LITHUANIA5_LITHUANIAN_EMPIRE_declined > 0
				ui_indicator_remove 0
				ui_indicator_remove 1
			end_if				
			if not I_IsFactionAIControlled lithuania
				inc_event_counter lithuania5_lithuanian_empire_player 1
			end_if
		end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;==========================================================================================================================
;====================================================================================================== Lithuania EVOLUTION
;---------- Cumans Evolution

;-------------------- Cumans : setting initial values of counters at the beginning of the game
set_event_counter cumans2_x 0						; turn 80:
set_event_counter cumans3_player 0			; turn 200: Mindaugas
set_event_counter cumans4_x 0						; turn 400
set_event_counter cumans5_player 0	; turn 500:
set_event_counter cumans6_x 0						; turn 600


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType cumans

	;-------------------- Cumans stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
		
	if not I_SettlementOwner Sharukan = cumans
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements cumans > 0
	and I_NumberOfSettlements cumans < 8
	
		log -------------------- Cumans lacking capital - nobles' financial help
		console_command add_money cumans, 5000

		if not I_IsFactionAIControlled cumans
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;=======================================================================================================================
;-------- Orthodox Factions
;=======================================================================================================================
;=======================================================================================================================

;==========================================================================================================================
;====================================================================================================== BYZANTINE EVOLUTION
;---------- Byzantine Evolutions

; this is only a plan:
set_event_counter byzantium2_anna_comnena_player 0			; turn 60-80
set_event_counter byzantium3_norman_wars_player 0			; turn 100-150
set_event_counter byzantium4_pronoia_player 0				; turn 160-200
set_event_counter byzantium6_renaissance_player 0			; turn 300
set_event_counter byzantium7_civil_wars_player 0			; turn 400


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType byzantium

	;-------------------- Byzantium stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Constantinople = byzantium
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements byzantium > 0
	and I_NumberOfSettlements byzantium < 8
	
		log -------------------- Byzantium lacking capital - nobles' financial help
		console_command add_money byzantium, 5000

		if not I_IsFactionAIControlled byzantium
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if


	if not I_IsFactionAIControlled byzantium
		log -------------------- Byzantium evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			inc_recruit_pool Constantinople_Province -1 Scoutatoi Swordsmen
			inc_recruit_pool Adrianople_Province -1 Stratiotae
			inc_recruit_pool Smyrna_Province -1 Stratiotae
		end_if
		if I_TurnNumber == 0
			historic_event BYZANTIUM0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event BYZANTIUM1_REFORMS_INFO
		end_if
	end_if
	

	if I_SettlementOwner Constantinople = byzantium			; at least the capital is in the Byzantine hands
	and RandomPercent < 40

		if not I_EventCounter BYZANTIUM2_ANNA_COMNENA == 1
			log -------------------- Byzantium evolution: (2) ANNA COMNENA ----------------------------------------
			; PLACEHOLDER !!!
			set_event_counter continue 0

			if I_TurnNumber > 35
			and I_EventCounter FL_is_crowned_ruler == 1
			and not I_IsFactionAIControlled byzantium					; to ensure it's not another faction FL who's got the crown - to be changed
				set_event_counter continue 1
			end_if

			if I_TurnNumber > 60										; FALL BACK - in case of failure or a bug
			and I_NumberOfSettlements byzantium > 0						; Byzantium exists
				set_event_counter continue 1
			end_if

			if I_EventCounter continue = 1
				set_event_counter continue 0
				historic_event BYZANTIUM2_ANNA_COMNENA factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, rum, georgia, zengid, jerusalem, abbasid, }
				if not I_IsFactionAIControlled byzantium
					set_event_counter byzantium2_anna_comnena_player 1
				end_if
			end_if
		end_if

		if not I_EventCounter BYZANTIUM3_NORMAN_WARS == 1
		and I_EventCounter BYZANTIUM2_ANNA_COMNENA = 1
			log -------------------- Byzantium evolution: (3) NORMAN WARS ----------------------------------------
			; PLACEHOLDER !!!
			; use the EBII codes as a template: number of great battles against Sicily
			if I_TurnNumber > 100
			and I_NumberOfSettlements byzantium > 0						; Byzantium exists
				historic_event BYZANTIUM3_NORMAN_WARS factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, rum, georgia, zengid, jerusalem, abbasid, }
				if not I_IsFactionAIControlled byzantium
					set_event_counter byzantium3_norman_wars_player 1
				end_if
			end_if
		end_if

		if not I_EventCounter BYZANTIUM4_PRONOIA == 1
		and I_EventCounter BYZANTIUM3_NORMAN_WARS = 1
			log -------------------- Byzantium evolution: (4) PRONOIA ----------------------------------------
			; PLACEHOLDER !!!
			; Pronoia system spread in 13th century
			; perhaps number of the landowners should be used as a condition
			set_event_counter continue 0

			if I_TurnNumber > 160
			and I_EventCounter FL_is_crowned_ruler == 1
			and not I_IsFactionAIControlled byzantium
				set_event_counter continue 1
			end_if

			if I_TurnNumber > 200
			and I_NumberOfSettlements byzantium > 0						; Byzantium exists				
				set_event_counter continue 1
			end_if

			if I_EventCounter continue = 1
				set_event_counter continue 0
				historic_event BYZANTIUM4_PRONOIA factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, rum, georgia, zengid, jerusalem, abbasid, }
				if not I_IsFactionAIControlled byzantium
					set_event_counter byzantium4_pronoia_player 1
				end_if
			end_if
		end_if
	end_if

;-------------------------------------------------------------------- BYZANTIUM Renaissance
; cultural changes during Paleologian age - after the fall of Constantinople to the Latins
; perhaps number of the landowners should be condition

	if not I_EventCounter BYZANTIUM5_BYZANTINE_RENAISSANCE_ALLOWED == 1	; fire only once
	and I_EventCounter BYZANTIUM4_PRONOIA == 1
	and not I_SettlementOwner Constantinople = byzantium			; lost Constantinople
	and not I_SettlementOwner Constantinople = slave				; but not to slaves
	and I_NumberOfSettlements byzantium > 0							; Byzantium exists
		log -------------------- Byzantium evolution: (5) RENAISSANCE ALLOWED ----------------------------------------
		historic_event BYZANTIUM5_BYZANTINE_RENAISSANCE_ALLOWED
	end_if

	if not I_EventCounter BYZANTIUM6_BYZANTINE_RENAISSANCE_STARTED == 1
	and I_SettlementOwner Constantinople = Byzantium
	and RandomPercent < 40
		log -------------------- Byzantium evolution: (6) RENAISSANCE STARTED ----------------------------------------
		set_event_counter continue 0

		if I_EventCounter BYZANTIUM5_BYZANTINE_RENAISSANCE_ALLOWED == 1		; it also means "after BYZANTIUM4_PRONOIA"
		and I_CompareCounter number_ikoner_studio_built > 4
		and I_EventCounter FL_is_crowned_ruler = 1
			set_event_counter continue 1
		end_if

		if I_TurnNumber > 300												; fall-back in case Constantinople was not lost or it's AI
		and I_NumberOfSettlements byzantium > 0								; Byzantium exists
			set_event_counter continue 1
		end_if

		if I_EventCounter continue = 1
			set_event_counter continue 0
			log ------ byzantium renaissance started
			historic_event BYZANTIUM6_BYZANTINE_RENAISSANCE_STARTED factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, rum, georgia, zengid, jerusalem, abbasid, }
			if not I_IsFactionAIControlled byzantium
				set_event_counter byzantium6_renaissance_player 1
			end_if
		end_if
	end_if

	;---------------------------------------------- events --------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;====================================================================================================== SERBIA EVOLUTION
;---------- Serbia Evolution

;----- in the future one may code two alternative paths of Serbian evolution: western (current) and orthodox
; it may impact:
; - units - make available for Serbia the Greek units
; - buildings, eg.: use scirpt ;---------- Racing Track  instead of Tourney Fields

; to be implemented: SERBIAN5_WESTERN_INFLUENCES - here in in EDB

;----- Patch of westernization - use it in the EDB

;-------------------- Serbia : setting initial values of counters at the beginning of the game
set_event_counter serbian2_nemanja_state_player 0			; turn 80-140
set_event_counter serbian3_saint_sava_player 0				; turn 150-220
; perhaps the Mongol invasion would play a role here
set_event_counter serbian4_dusan_empire_player 0			; turn 400
set_event_counter serbian5_western_influences_player 0		; turn 500 - perhaps make two different events Latin vs Byzantine
set_event_counter serbian6_brankovic_last_stand_player 0	; turn 600

set_event_counter serbia_initial_support_choice_made 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType serbia

	;-------------------- Serbia : setting initial values of counters at the beginning of each turn
	set_event_counter serbia_supported_due_to_capital_occupation 0


	;-------------------- Serbia stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Ras = serbia
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements serbia > 0
	and I_NumberOfSettlements serbia < 8
	
		log -------------------- Serbia lacking capital - nobles' financial help
		console_command add_money serbia, 5000
		set_event_counter serbia_supported_due_to_capital_occupation 1
	
		if not I_IsFactionAIControlled serbia
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if


	;-------------------- Serbia stability : (B) Support if the FACTION IS SMALL ------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-3 settlements, smaller for 4-5
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)

	if I_CompareCounter pl_ec_id == 25 ; serbia

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter serbia_initial_support_choice_accepted == 0
		and I_EventCounter serbia_initial_support_choice_declined == 0
			log --- script start: Player decides if weak serbia should be supported ------------
			historic_event serbia_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter serbia_initial_support_choice_accepted == 0
				and I_EventCounter serbia_initial_support_choice_declined == 0
				end_while
			end_if
			
		end_if

		; give money to player: if player accepted financial support, serbia has not already been supported (due to capital loss), serbia has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter serbia_initial_support_choice_accepted == 1
		and I_EventCounter serbia_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements serbia < 5
		and I_TurnNumber > 0
			log --- script start : serbia supported when the player is serbia ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements serbia > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money serbia, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event serbia_initial_support
		end_if
	end_if

	if I_TurnNumber > 0
	and I_EventCounter serbia_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
		if I_EventCounter faction_turn_serbia == 1
		and I_IsFactionAIControlled serbia
			log --- script start : Serbia supported when the player is NOT Serbia				
			if I_EventCounter faction_size_serbia_small == 1
				console_command add_money serbia, 5000
			end_if
			if I_EventCounter faction_size_serbia_medium == 1
				console_command add_money serbia, 3000
			end_if
		end_if
		if I_CompareCounter pl_ec_id == 17 ; byzantium
			log --- script start : Serbia supported when the player is BYZANTIUM (add-on)
			if I_EventCounter faction_size_serbia_small == 1
				console_command add_money serbia, 5000
			end_if
			if I_EventCounter faction_size_serbia_medium == 1
				console_command add_money serbia, 3000
			end_if
		end_if
	end_if

	if not I_IsFactionAIControlled serbia
	and I_EventCounter SERBIAN0_STARTING_INFO == 0

		log -------------------- Serbia evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event SERBIAN0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event SERBIAN1_REFORMS_INFO
		end_if
	end_if
	
	if RandomPercent < 40

		if I_EventCounter SERBIAN2_NEMANJA_STATE == 0
		and I_NumberOfSettlements serbia > 0				; Serbia exists
		and I_TurnNumber > 99
			log -------------------- Serbian evolution: (2) NEMANJA STATE ----------------------------------------
			; Stefan Nemanja state (1166-1196)
			historic_event SERBIAN2_NEMANJA_STATE factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, pisa, rum, }
			if not I_IsFactionAIControlled serbia
				inc_event_counter serbian2_nemanja_state_player 1
			end_if
		end_if

		if I_EventCounter SERBIAN3_SAINT_SAVA == 0
		and I_EventCounter SERBIAN2_NEMANJA_STATE = 1
		and I_NumberOfSettlements serbia > 0				; Serbia exists
		and I_TurnNumber > 199
			log -------------------- Serbian evolution: (3) SAINT SAVA ----------------------------------------
			; Saint Sava cultural modernization (1174-1236)
			historic_event SERBIAN3_SAINT_SAVA factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, pisa, rum, }
			if not I_IsFactionAIControlled serbia
				inc_event_counter serbian3_saint_sava_player 1
			end_if
		end_if

		if I_EventCounter SERBIAN4_DUSAN_EMPIRE = 0
		and I_EventCounter SERBIAN3_SAINT_SAVA = 1
		and I_NumberOfSettlements serbia > 0				; Serbia exists
		and I_TurnNumber > 399
			log -------------------- Serbian evolution: (4) STEFAN DUSAN EMPIRE ----------------------------------------
			; Stefan Dusan state (1330-1355)
			historic_event SERBIAN4_DUSAN_EMPIRE factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, pisa, rum, }
			if not I_IsFactionAIControlled serbia
				inc_event_counter serbian4_dusan_empire_player 1
			end_if
		end_if

		if I_EventCounter SERBIAN6_BRANKOVIC_LAST_STAND = 0
		and I_EventCounter SERBIAN4_DUSAN_EMPIRE = 1
		and I_NumberOfSettlements serbia > 0				; Serbia exists
		and I_TurnNumber > 599
			log -------------------- Serbian evolution: (6) BRANKOVIC DYNASTY ----------------------------------------
			;  Brankovic dynasty (1427-1459)
			historic_event SERBIAN6_BRANKOVIC_LAST_STAND factions { byzantium, hungary, poland, hre, venice, kievan_rus, cumans, sicily, pisa, rum, }
			if not I_IsFactionAIControlled serbia
				inc_event_counter serbian6_brankovic_last_stand_player 1
			end_if
			terminate_monitor
		end_if
	end_if
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;========================================================================================================================
;====================================================================================================== GEORGIA EVOLUTION
;---------- Georgia Evolution
; counters used in EDB for availability of the units, but also benefits from the buildings (library, equestrian etc.)

set_event_counter georgian2_golden_age_player 0				; turn 80-140
set_event_counter georgian3_shota_rustaveli_player 0		; turn 150-220
set_event_counter georgian4_crossbow_in_caucasus_player 0	; turn 200-300
set_event_counter georgian6_revival_player 0				; turn 270-360
set_event_counter georgian7_last_united_kingdom_player 0	; turn 520-600
set_event_counter georgian8_tatar_lancers_reform_player 0	; turn 70-160

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType georgia

	if not I_IsFactionAIControlled georgia
	log -------------------- Georgia evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event GEORGIAN0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event GEORGIAN1_REFORMS_INFO
		end_if
		if I_TurnNumber == 4
			historic_event GEORGIAN1B_MILITARY_REFORMS_INFO
		end_if
	end_if

	;-------------------- Georgia stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Tbilisi = georgia
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements georgia > 0
	and I_NumberOfSettlements georgia < 8
	
		log -------------------- Georgia lacking capital - nobles' financial help
		console_command add_money georgia, 5000

		if not I_IsFactionAIControlled georgia
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
	if not I_EventCounter GEORGIAN2_GOLDEN_AGE == 1
	and RandomPercent < 40
	and I_SettlementOwner Tbilisi = georgia
	log -------------------- Georgia evolution: (2) GOLDEN AGE ----------------------------------------

		; historically should take place around turn 140
		set_event_counter continue 0
		if I_TurnNumber > 70											; just in case the player rushes the map
		and I_SettlementOwner Kutaisi = georgia							; Geogia holds core regions
			if I_CompareCounter number_landowners2_built > 2			; Georgia built 3+ Local Guards (Large Town level)
			and I_CompareCounter number_ikoner_studio_built > 1			; Georgia built 2+ Ikoner Studio (Minor City level)
				if I_FactionLeaderTrait georgia WifeIsGreek > 0			; the FL should have either wife or some ancestors of Byzantine / Kievian descent
					set_event_counter continue 1
				end_if
				if I_FactionLeaderTrait georgia Royal_Relation_Greek > 0
					set_event_counter continue 1
				end_if
				if I_FactionLeaderTrait georgia WifeIsKievan > 0
					set_event_counter continue 1
				end_if
				if I_FactionLeaderTrait georgia Royal_Relation_Kievan > 0
					set_event_counter continue 1
				end_if
			end_if
			if I_EventCounter FL_is_crowned_ruler == 1						; FL has the crown
			and not I_IsFactionAIControlled georgia							; to ensure it's not another faction FL who's got the crown
				set_event_counter continue 1
			end_if

			if I_TurnNumber > 130											; FALL BACK - in case of failure or a bug
				set_event_counter continue 1
			end_if
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event GEORGIAN2_GOLDEN_AGE factions { georgia, jerusalem, byzantium, turks, cumans, abbasid, rum, zengid, kievan_rus, }
			if not I_IsFactionAIControlled georgia
				inc_event_counter georgian2_golden_age_player 1
			end_if
		end_if
	end_if

	if not I_EventCounter GEORGIAN3_SHOTA_RUSTAVELI == 1
	and I_EventCounter GEORGIAN2_GOLDEN_AGE = 1
	and I_TurnNumber > 150
	and RandomPercent < 40											; to add some randomness against the bugs
	log -------------------- Georgia evolution: (3) SHOTA RUSTAVELI ----------------------------------------

		; historically should take place around turn 170
		set_event_counter continue 0
		if I_SettlementOwner Tbilisi = georgia 							; at least the capital is in the Georgian hands
			if I_CompareCounter number_ikoner_studio_built > 3			; Gergia has built 3 Ikoner Studios (Minor City level)
			and I_CompareCounter number_cathedral_o_built > 1			; at least 2 Large City
			and I_SettlementOwner Kutaisi = georgia
				set_event_counter continue 1
			end_if
			if I_TurnNumber > 210										; FALL BACK - in case of failure or a bug
				set_event_counter continue 1
			end_if
		end_if
		if I_SettlementOwner Jerusalem = georgia ; automatic if Georgia conquers Jerusalem
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event GEORGIAN3_SHOTA_RUSTAVELI factions { georgia, jerusalem, byzantium, turks, cumans, abbasid, rum, zengid, kievan_rus, }
			if not I_IsFactionAIControlled georgia
				inc_event_counter georgian3_shota_rustaveli_player 1
			end_if
		end_if

	end_if

	if not I_EventCounter GEORGIAN4_CROSSBOW_IN_CAUCASUS == 1
	and I_EventCounter GEORGIAN3_SHOTA_RUSTAVELI = 1
	and RandomPercent < 40
	and I_TurnNumber > 200
	and I_SettlementOwner Tbilisi = georgia
	and I_EventCounter MOUNTED_CROSSBOWS > 0						; improvments in crossbow technology in Europe occured
	log -------------------- Georgia evolution: (4) CROSSBOW IN CAUCASUS ----------------------------------------

		; add crossbow units for Georgia
		; this event is unrelated to the next Georgian events (may happen later)
		; there's no pic
		if I_CompareCounter number_university_built > 0					; at least 1 Large City (allows contacts with the Latins)
		and I_CompareCounter number_marksmans_range_built > 1			; Gergia has built 2 Marksmans Ranges (Castle level) (as player)
		and I_SettlementOwner Kutaisi = georgia
			if I_FactionLeaderTrait georgia WifeIsJerusalem > 0			; the FL should have either wife or some ancestors of Byzantine / Kievian descent
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia Royal_Relation_Jerusalem > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia WifeIsHRE > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia Royal_Relation_HRE > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia WifeIsEnglish > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia Royal_Relation_English > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia WifeIsSicilian > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia Royal_Relation_Sicilian > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia WifeIsFrench > 0
				set_event_counter continue 1
			end_if
			if I_FactionLeaderTrait georgia Royal_Relation_French > 0
				set_event_counter continue 1
			end_if
		end_if
		if I_TurnNumber > 290											; FALL BACK - in case of failure or a bug
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event GEORGIAN4_CROSSBOW_IN_CAUCASUS factions { georgia, jerusalem, byzantium, turks, cumans, abbasid, rum, zengid, kievan_rus, }
			if not I_IsFactionAIControlled georgia
				inc_event_counter georgian4_crossbow_in_caucasus_player 1
			end_if
		end_if

	end_if

	if not I_EventCounter GEORGIAN5_REVIVAL_ALLOWED = 1  	; no randomness here as it should happen as soon as conditions are met: this is just a pre-condition for Georgian Revival
	and I_EventCounter GEORGIAN3_SHOTA_RUSTAVELI = 1
	log -------------------- Georgia evolution: (5) REVIVAL ALLOWED ----------------------------------------

		if not I_SettlementOwner Tbilisi = georgia
		and not I_SettlementOwner Tbilisi = slave
			historic_event GEORGIAN5_REVIVAL_ALLOWED
		end_if
		if I_TurnNumber > 270 	; fallback
			historic_event GEORGIAN5_REVIVAL_ALLOWED
		end_if
	end_if

	if not I_EventCounter GEORGIAN6_REVIVAL == 1
	and I_EventCounter GEORGIAN5_REVIVAL_ALLOWED = 1
	and RandomPercent < 40
	and I_SettlementOwner Tbilisi = georgia
	log -------------------- Georgia evolution: (6) GEORGIAN REVIVAL ----------------------------------------

		; historically should take place around turn 380
		set_event_counter continue 0
		if I_TurnNumber > 270
		and I_CompareCounter number_landowners3_built > 3				; Gergia has built 4 Local Councils (Minor City level)
		and I_CompareCounter number_citadel_built > 0					; at least 1 Citadel (Large Castle) exists (as player)
			set_event_counter continue 1
		end_if
		if I_TurnNumber > 350											; FALL BACK - in case of failure or a bug
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event GEORGIAN6_REVIVAL factions { georgia, jerusalem, byzantium, turks, cumans, abbasid, rum, zengid, kievan_rus, }
			if not I_IsFactionAIControlled georgia
				inc_event_counter georgian6_revival_player 1
			end_if
		end_if
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;-------------------------------------------------------------------- GEORGIAN 7 LAST UNITED KINGDOM
; historically should take place around turn 600
; details of this event are to be worked out in the future (the description is ok, but there's no pic)

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType georgia
and FactionBuildingExists >= huge_cathedral_o					; at least 1 Huge City
and FactionBuildingExists >= great_university					; at least 1 Huge City
and FactionBuildingExists >= royal_armoury						; highest level of barracks in a citadel
and RandomPercent < 40
and I_SettlementOwner Tbilisi = georgia
	set_event_counter continue 0
	if I_TurnNumber > 520
	and I_SettlementOwner Kutaisi = georgia
		set_event_counter continue 1
	end_if
	if I_TurnNumber > 600										; FALL BACK - in case of failure or a bug
		set_event_counter continue 1
	end_if
	if I_EventCounter continue = 1
		set_event_counter continue 0
		historic_event GEORGIAN7_LAST_UNITED_KINGDOM factions { georgia, jerusalem, byzantium, turks, cumans, abbasid, rum, zengid, kievan_rus, }
		if not I_IsFactionAIControlled georgia
			inc_event_counter georgian7_last_united_kingdom_player 1
		end_if
		terminate_monitor
	end_if
end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;-------------------------------------------------------------------- GEORGIAN 8 TATAR LANCERS REFORMS
; the blueprint for this script are the EBII Pontos reforms
; it is likely to happen within the first 70-150 turns (backstop at turn 160)
; in the future it will be streamlined as far as the number of the monitors is concerned

declare_counter georgia_battlewon

monitor_event PostBattle FactionType georgia
	and WonBattle
	and GeneralFoughtFaction cumans
	and not I_ConflictType Naval
	and not I_ConflictType Withdraw
	inc_counter georgia_battlewon 1
	if I_IsFactionAIControlled georgia					; not for the AI
		terminate_monitor
	end_if
	if I_CompareCounter georgia_battlewon > 10
		terminate_monitor
	end_if
end_monitor

monitor_event PostBattle FactionType georgia
	and WonBattle
	and GeneralFoughtFaction turks
	and not I_ConflictType Naval
	and not I_ConflictType Withdraw
	inc_counter georgia_battlewon 1
	if I_IsFactionAIControlled georgia					; not for the AI
		terminate_monitor
	end_if
	if I_CompareCounter georgia_battlewon > 10
		terminate_monitor
	end_if
end_monitor

monitor_event PostBattle FactionType georgia
	and WonBattle
	and GeneralFoughtFaction rum
	and not I_ConflictType Naval
	and not I_ConflictType Withdraw
	inc_counter georgia_battlewon 1
	if I_IsFactionAIControlled georgia					; not for the AI
		terminate_monitor
	end_if
	if I_CompareCounter georgia_battlewon > 10
		terminate_monitor
	end_if
end_monitor

monitor_event PostBattle FactionType georgia
	and WonBattle
	and GeneralFoughtFaction mongols
	and not I_ConflictType Naval
	and not I_ConflictType Withdraw
	inc_counter georgia_battlewon 1
	if I_IsFactionAIControlled georgia					; not for the AI
		terminate_monitor
	end_if
	if I_CompareCounter georgia_battlewon > 10
		terminate_monitor
	end_if
end_monitor

monitor_event FactionTurnStart FactionType georgia

	if I_CompareCounter georgia_battlewon > 9
		and I_SettlementOwner Kutaisi = georgia
		and I_SettlementOwner Tbilisi = georgia
		set_event_counter georgian8_tatar_lancers_reform_player 1
		historic_event GEORGIAN8_TATAR_LANCERS_REFORM factions { georgia, }
		terminate_monitor
	end_if

	if I_TurnNumber > 160										; FALL BACK - in case of failure or a bug
		and RandomPercent < 10									; to add some randomness against the bugs
		historic_event GEORGIAN8_TATAR_LANCERS_REFORM factions { georgia, }
		terminate_monitor
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;====================================================================================================================
;====================================================================================================== RUS EVOLUTION
;---------- Rus Evolution

;-------------------- Rus : setting initial values of counters at the beginning of the game

set_event_counter rus2_moscow_player 0					; turn 30: MOSCOW_FOUNDED
														; turn 70: EUPHROSYNE
set_event_counter rus3_sobors_player 0					; turn 150-220: all highest level cathedrals built (Novgorod, Kiev, Pskov)
														; turn 220-250 : MONGOLS_INVADE_ZALESYE
set_event_counter rus4_moscov_tver_novgorod_player 0	; turn 330: RUS4_MOSCOV_TVER_NOVGOROD
set_event_counter rus5_andrey_rublyov_player 0			; turn 400: RUS5_ANDREY_RUBLYOV: changes in culture
set_event_counter rus6_ugra_river_player 0				; turn ?: RUS6_UGRA_RIVER:  victory over the steppe people
set_event_counter rus7_third_rome_player 0				; turn 600 RUS7_THIRD_ROME: reforms during the reign of Ivan III

set_event_counter novgorod_initial_support_choice_made 0


; Ideas for Kiev ----------------------------------------
; historically, in 1132 there's a breakdown of the ruler's legitimacy in the wake of the Mstislav death
; one should script it somehow

; Ideas for Novgorod ----------------------------------------
; - once Hanzeatic Kontor is built? Or just a Sobor?
; - make Novgorod great again - Voroti lvl4, SofijskijSobor

monitor_event BuildingCompleted TrueCondition
and SettlementBuildingFinished = wonder_kiev_zoloti_vorota_3big					; Kiev must grow to large city
	inc_event_counter zoloti_vorota_3big_built 1								; used in EDB
	terminate_monitor
end_monitor


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType russia

	;-------------------- Novgorod : setting initial values of counters at the beginning of each turn
	set_event_counter novgorod_supported_due_to_capital_occupation 0


	;-------------------- Novgorod stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Novgorod = russia
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements russia > 0
	and I_NumberOfSettlements russia < 8
	
		log -------------------- Novgorod lacking capital - nobles' financial help
		console_command add_money russia, 5000
		set_event_counter novgorod_supported_due_to_capital_occupation 1
	
		if not I_IsFactionAIControlled russia
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

	;-------------------- Novgorod stability : (B) Support if the FACTION IS SMALL ------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-2 settlements, smaller for 3-4
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)

	if I_CompareCounter pl_ec_id == 19 ;russia

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter novgorod_initial_support_choice_accepted == 0
		and I_EventCounter novgorod_initial_support_choice_declined == 0
			log --- script start: Player decides if weak novgorod should be supported ------------
			historic_event novgorod_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter novgorod_initial_support_choice_accepted == 0
				and I_EventCounter novgorod_initial_support_choice_declined == 0
				end_while
			end_if
			
		end_if

		; give money to player: if player accepted financial support, novgorod has not already been supported (due to capital loss), novgorod has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter novgorod_initial_support_choice_accepted == 1
		and I_EventCounter novgorod_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements russia < 5
		and I_TurnNumber > 0
			log --- script start : novgorod supported when the player is novgorod ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements russia > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money russia, 1000
				inc_event_counter initial_support_generosity -1
			end_while					
			historic_event novgorod_initial_support
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 24 ; kievan_rus
	and I_EventCounter novgorod_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Novgorod supported when the player is KIEV
		
		if I_EventCounter faction_size_russia_small == 1
			console_command add_money russia, 5000
		end_if
		if I_EventCounter faction_size_russia_medium == 1
			console_command add_money russia, 2000
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 16 ; poland
	and I_EventCounter novgorod_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 100
	
		log --- script start : Novgorod supported when the player is POLAND (later in the game)
		
		if I_EventCounter faction_size_russia_small == 1
			console_command add_money russia, 5000
		end_if
		if I_EventCounter faction_size_russia_medium == 1
			console_command add_money russia, 2000
		end_if
	end_if


log -------------------- Rus evolution: (0 1) INFORMATION ----------------------------------------
	
log -------------------- Rus evolution: (3) MOSCOW ----------------------------------------
; Crossbowmen available for Kiev

	if I_EventCounter MOSCOW_FOUNDED < 1				; turn 50-80
	and RandomPercent < 50
		set_event_counter continue 0
		if I_TurnNumber > 30
		and I_SettlementOwner Rustov = russia
		and I_SettlementOwner Suzdal = russia
		and I_SettlementOwner Ryazan = russia
			set_event_counter continue 1
		end_if
		if I_TurnNumber > 80
		and I_SettlementOwner Novgorod = russia
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event MOSCOW_FOUNDED factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus2_moscow_player 1
			end_if
		end_if
	end_if

	if I_EventCounter EUPHROSYNE < 1				; turn 140
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter HEAVY_MAIL_ARMOR > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event EUPHROSYNE factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus3_sobors_player 1
			end_if
		end_if
	end_if

	if I_EventCounter RUS_CROSSBOWS < 1						; turn 210
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter high_era > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event RUS_CROSSBOWS factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus3_sobors_player 1
			end_if
		end_if
	end_if

	if I_EventCounter RUS4_MOSCOV_TVER_NOVGOROD < 1 		; turn 325
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter CANNONS > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event RUS4_MOSCOV_TVER_NOVGOROD factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus4_moscov_tver_novgorod_player 1
			end_if
		end_if
	end_if

	if I_EventCounter RUS5_ANDREY_RUBLYOV < 1				; turn 440
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter HALF_PLATE_ARMOR > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event RUS5_ANDREY_RUBLYOV factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus5_andrey_rublyov_player 1
			end_if
			terminate_monitor
		end_if
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


; @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
; WARNING: THIS IS JUST A COPY OF THE PREVIOUS - MAKE ALL THE CHANGES BEFORE AND ONLY LATER DOUBLE IT FOR KIEVAN_RUS - but the financial script!

monitor_event PreFactionTurnStart FactionType kievan_rus

	;-------------------- kievan_rus stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Kiev = kievan_rus
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements kievan_rus > 0
	and I_NumberOfSettlements kievan_rus < 8
	
		log -------------------- kievan_rus lacking capital - nobles' financial help
		console_command add_money kievan_rus, 5000

		if not I_IsFactionAIControlled kievan_rus
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Rus evolution: (0 1) INFORMATION ----------------------------------------



log -------------------- Rus evolution: (3) MOSCOW ----------------------------------------
; Crossbowmen available for Kiev

	if I_EventCounter MOSCOW_FOUNDED < 1
	and RandomPercent < 50
		set_event_counter continue 0
		if I_TurnNumber > 30
		and I_SettlementOwner Rustov = kievan_rus
		and I_SettlementOwner Suzdal = kievan_rus
		and I_SettlementOwner Ryazan = kievan_rus
			set_event_counter continue 1
		end_if
		if I_TurnNumber > 80
		and I_SettlementOwner Kiev = kievan_rus
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event MOSCOW_FOUNDED factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus2_moscow_player 1
			end_if
		end_if
	end_if

	if I_EventCounter EUPHROSYNE < 1
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter HEAVY_MAIL_ARMOR > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event EUPHROSYNE factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus3_sobors_player 1
			end_if
		end_if
	end_if

	if I_EventCounter RUS_CROSSBOWS < 1
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter OSTSIEDLUNG > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event RUS_CROSSBOWS factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus3_sobors_player 1
			end_if
		end_if
	end_if

	if I_EventCounter RUS4_MOSCOV_TVER_NOVGOROD < 1
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter CANNONS > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event RUS4_MOSCOV_TVER_NOVGOROD factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus4_moscov_tver_novgorod_player 1
			end_if
		end_if
	end_if

	if I_EventCounter RUS5_ANDREY_RUBLYOV < 1
	and RandomPercent < 50
		set_event_counter continue 0
		if I_EventCounter HALF_PLATE_ARMOR > 0
			set_event_counter continue 1
		end_if
		if I_EventCounter continue = 1
			set_event_counter continue 0
			historic_event RUS5_ANDREY_RUBLYOV factions { russia, kievan_rus, hungary, poland, cumans, norway, denmark, byzantium, }
			if not I_IsFactionAIControlled russia
				inc_event_counter rus5_andrey_rublyov_player 1
			end_if
			terminate_monitor
		end_if
	end_if

end_monitor



;=======================================================================================================================
;=======================================================================================================================
;-------- Muslim Dynasties
;=======================================================================================================================
;=======================================================================================================================


;=======================================================================================================================
;====================================================================================================== RUM EVOLUTION
;---------- Seljuks Evolution

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType turks
	
log -------------------- Seljuks evolution: (0 1) INFORMATION ----------------------------------------

	;-------------------- turks stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Isfahan = turks
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements turks > 0
	and I_NumberOfSettlements turks < 8
	
		log -------------------- turks lacking capital - nobles' financial help
		console_command add_money turks, 5000

		if not I_IsFactionAIControlled turks
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Seljuks evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;=====================================================================================================================	
;====================================================================================================== RUM EVOLUTION
;---------- Rum Evolution

set_event_counter rum2_danishmendids_player 0			; turn 60:
set_event_counter rum3_kose_dag_player 0				; turn 150-220:
set_event_counter rum4_x 0				; turn 400:
set_event_counter rum5_x 0				; turn 500:
set_event_counter rum6_x 0				; turn 600

set_event_counter rum_initial_support_choice_made 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType rum


	;-------------------- Rum : setting initial values of counters at the beginning of each turn
	set_event_counter rum_supported_due_to_capital_occupation 0


	;-------------------- Rum stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Konya = rum
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements rum > 0
	and I_NumberOfSettlements rum < 8
	
		log -------------------- Rum lacking capital - nobles' financial help
		console_command add_money rum, 5000
		set_event_counter rum_supported_due_to_capital_occupation 1
	
		if not I_IsFactionAIControlled rum
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if


	;-------------------- Rum stability : (B) Support if the FACTION IS SMALL ------------
	; For AI: large support for 1-3 settlements, smaller for 4-9.
	; For the player: random support, large for 1-2 settlements, smaller for 3-4
	; This is a solution to the problem of initial inability of this faction to support fighting, but will work throughout the whole game
	; Programing consideration: it could fire at the end of the turn so that it happens before the debt-warning / disbandment of the mercenaries. However, it probably comes before the AI-debt script (that cancels the AI debte if it plunges into), so it will not be explosionary (add money -> recruitment -> debt -> cancel debt -> add money -> more recruitment -> higher debt -> cancel debt -> add money ...)

	if I_CompareCounter pl_ec_id == 31 ; rum

		; offer support to player: if player has not been offered support and/or has not awnsered (in rare case of save/reload without awnser)
		;	- trigger historic event
		;	- loop to wait for awnser if no turn 1
		; WARNING: events are case-sensitive when tested by I_EventCounter --- Belo
		if I_EventCounter rum_initial_support_choice_accepted == 0
		and I_EventCounter rum_initial_support_choice_declined == 0
			log --- script start: Player decides if weak rum should be supported ------------
			historic_event rum_initial_support_choice true
			if I_TurnNumber > 0
				while I_EventCounter rum_initial_support_choice_accepted == 0
				and I_EventCounter rum_initial_support_choice_declined == 0
				end_while
			end_if
			
		end_if

		; give money to player: if player accepted financial support, rum has not already been supported (due to capital loss), rum has less than 5 settlements and it's not turn 0:
		;	- create a random counter
		;	- +2 to random counter if faction has more than 2 settlements
		;	- give money depending on counter result
		;	- add (value of random counter * 1000) gold to faction
		; 	- then trigger historic event to inform the player money has been given to him
		; NOTE: using faction_size_smallormedium lead to a case where no money is given if player has between 4 and 10 settlements, better directly use I_NumberOfSettlements instead --- Belo
		if I_EventCounter rum_initial_support_choice_accepted == 1
		and I_EventCounter rum_supported_due_to_capital_occupation < 1
		and I_NumberOfSettlements rum < 5
		and I_TurnNumber > 0
			log --- script start : rum supported when the player is rum ------------
			generate_random_counter initial_support_generosity 1 3
			if I_NumberOfSettlements rum > 2
				inc_event_counter initial_support_generosity 2
			end_if
			while I_EventCounter initial_support_generosity > 0
				console_command add_money rum, 1000
				inc_event_counter initial_support_generosity -1
			end_while
			historic_event rum_initial_support
		end_if
	end_if

	if I_TurnNumber > 0
	and I_EventCounter rum_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
		if I_EventCounter faction_turn_rum == 1
		and I_IsFactionAIControlled rum
			log --- script start : Rum supported when the player is NOT RUM
			if I_EventCounter faction_size_rum_small == 1
				console_command add_money rum, 5000
			end_if
			if I_EventCounter faction_size_rum_medium == 1
				console_command add_money rum, 3000
			end_if
		end_if
		if I_CompareCounter pl_ec_id == 17 ; byzantium
			log --- script start : Rum supported when the player is BYZANTIUM (add-on)
			if I_EventCounter faction_size_rum_small == 1
				console_command add_money rum, 5000
			end_if
			if I_EventCounter faction_size_rum_medium == 1
				console_command add_money rum, 3000
			end_if
		end_if
	end_if

	if not I_IsFactionAIControlled rum
	log -------------------- Rum evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event RUM0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event RUM1_REFORMS_INFO
		end_if
	end_if


	if not I_EventCounter RUM2_DANISHMENDIDS == 1
	and I_TurnNumber > 30
	and RandomPercent < 80
	and I_SettlementOwner Caesarea = rum
	and I_SettlementOwner Sives = rum
	and I_SettlementOwner Angora = rum
	and I_SettlementOwner Konya = rum
	log -------------------- Rum evolution: (2) DANISHMENDIDS ----------------------------------------

		historic_event RUM2_DANISHMENDIDS factions { rum, byzantium, serbia, georgia, zengid, jerusalem, }

		if not I_IsFactionAIControlled rum
			inc_event_counter rum2_danishmendids_player 1
		end_if

		; both conditions and effects to be developed

	end_if


	if not I_EventCounter RUM3_KOSE_DAG == 1
	and I_TurnNumber > 100
	and RandomPercent < 80
	and I_SettlementOwner Caesarea = rum
	and I_SettlementOwner Sives = mongols

	log -------------------- Rum evolution: (3) KOSE DAG ----------------------------------------

	; this should be related to a lost battle against the Mongols

		historic_event RUM3_KOSE_DAG factions { rum, byzantium, zengid, georgia, abbasid, egypt, jerusalem, hungary, venice, }

		if not I_IsFactionAIControlled rum
			inc_event_counter rum3_kose_dag_player 1
		end_if

		; both conditions and effects to be developed

	end_if


	;---------------------------------------------- events --------------------------------------
	if RandomPercent < 30

		if not I_EventCounter RUM20_MILLETI_RUM == 1
		and RandomPercent < 20
		and I_EventCounter FL_is_crowned_ruler == 1
		and I_SettlementOwner Constantinople = rum
		and I_SettlementOwner Nicaea = rum
		and I_SettlementOwner Smyrna = rum

		log -------------------- Millet-i Rum ----------------------------------------

			historic_event RUM20_MILLETI_RUM factions { rum, byzantium, zengid, georgia, abbasid, egypt, jerusalem, hungary, venice, }

			if not I_IsFactionAIControlled rum
				inc_event_counter rum20_milleti_rum_player 1
			end_if

		end_if

	end_if

end_monitor


;==========================================================================================================================
;====================================================================================================== ALMORAVID EVOLUTION
;---------- Almoravid Evolution

set_event_counter moors2_moscow 0			; turn 80-140:
set_event_counter moors3_sobors 0			; turn 150-220:
set_event_counter moors4_x 0				; turn 400:
set_event_counter moors5_x 0				; turn 500:
set_event_counter moors6_third_rome 0		; turn 600

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType Moors
	
log -------------------- Almoravid ----------------------------------------

	;-------------------- moors stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Marrakesh = moors
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements moors > 0
	and I_NumberOfSettlements moors < 8
	
		log -------------------- moors lacking capital - nobles' financial help
		console_command add_money moors, 5000

		if not I_IsFactionAIControlled moors
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
	if not I_IsFactionAIControlled moors
		log -------------------- Almoravid evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			inc_recruit_pool Sevilla_Province -1 Dismounted Arab Cavalry
			inc_recruit_pool Cordoba_Province -1 Dismounted Arab Cavalry
			inc_recruit_pool Granada_Province -1 Andalusian Infantry				
			inc_recruit_pool Granada_Province -1 Christian Guard
			inc_recruit_pool Granada_Province -1 Dismounted Christian Guard
		end_if
		if I_TurnNumber == 0
			historic_event MOORS0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event MOORS1_REFORMS_INFO
		end_if
	end_if
			
log -------------------- Almoravid evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;===================== GIRALDA EVENT ==============================
; add a fall-back option at turn 100 in case the AI doesn't build it
; but it's not yet a mechanism

monitor_event BuildingCompleted TrueCondition
	and SettlementBuildingFinished = great_jama
	and SettlementName Sevilla

	historic_event the_giralda
	terminate_monitor
end_monitor


;=======================================================================================================================
;====================================================================================================== ZENGID EVOLUTION
;---------- Zengid Evolution
; not reviewed as far the historicity of the mechanism is concerned - JoC

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType Abbasid
	
log -------------------- Zengid evolution: (0 1) INFORMATION ----------------------------------------

	;-------------------- Zengid stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Mosul = zengid
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements zengid > 0
	and I_NumberOfSettlements zengid < 8
	
		log -------------------- Zengid lacking capital - nobles' financial help
		console_command add_money zengid, 5000

		if not I_IsFactionAIControlled zengid
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Zengid evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;---------------------- player ---------------------------
monitor_event BuildingCompleted TrueCondition
	and NumBuildingsCompletedFaction landowners2 > 2				; should be replaced with counter number_landowners2_built
	and FactionType zengid

	if I_IsFactionAIControlled zengid
		terminate_monitor
	end_if

	if I_NumberOfSettlements zengid > 3
		and I_TurnNumber > 50
		and I_EventCounter zengids_rise < 1

		historic_event zengids true
		terminate_monitor
	end_if
end_monitor

monitor_event PreFactionTurnStart FactionType zengid

	if I_IsFactionAIControlled zengid
		terminate_monitor
	end_if

	if I_TurnNumber > 60
		and I_EventCounter zengids_rise < 1

		historic_event zengids true
		terminate_monitor
	end_if
end_monitor

monitor_event EventCounter EventCounterType zengids_accepted
	and EventCounter = 1

	historic_event zengids_rise

	add_events
		event	counter	first_mamluks_zengid
		date	50
		event	counter	mamluks_established_zengid
		date	100
	end_add_events

	set_event_counter zengids_accepted 0
	terminate_monitor
end_monitor

monitor_event EventCounter EventCounterType zengids_declined
	and EventCounter = 1

	add_events
		event	counter	first_mamluks_zengid
		date	80
		event	counter	mamluks_established_zengid
		date	120
	end_add_events

	set_event_counter zengids_declined 0
	terminate_monitor
end_monitor

monitor_event EventCounter EventCounterType first_mamluks_zengid
	and EventCounter = 1
	and I_EventCounter zengids_rise = 0

	historic_event zengids_rise

	terminate_monitor
end_monitor


;---------------------   AI --------------------------

monitor_event PreFactionTurnStart FactionType zengid

	if not I_IsFactionAIControlled zengid
		terminate_monitor
	end_if

	if I_TurnNumber > 60
		and I_NumberOfSettlements zengid > 2

		historic_event zengids_rise
		set_event_counter zengid_evolution 1
		add_events
			event	counter	first_mamluks_zengid
			date	50
			event	counter	mamluks_established_zengid
			date	100
		end_add_events
		terminate_monitor
	end_if
end_monitor

;===================== POSSIBLE ZENGID TO MAMLUK TRANSITION ========================

monitor_event BecomesFactionLeader FactionType zengid
	and not I_EventCounter mamluks_rise_zengid > 0

	if I_FactionLeaderTrait zengid Royal_Blood_Mamluk = 1
		historic_event mamluks_rise_zengid
	end_if

	terminate_monitor
end_monitor


;========================================================================================================================
;====================================================================================================== ABBASID EVOLUTION
;---------- Abbasid Evolution
; JoC 2022: difficult to understand why in the SSHIP we've got it since we've got a separate faction abbasids?!
; perhaps it's a leftover from the past

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType Abbasid
	
log -------------------- Abbasid evolution: (0 1) INFORMATION ----------------------------------------

	;-------------------- Abbasid stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Baghdad = abbasid
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements abbasid > 0
	and I_NumberOfSettlements abbasid < 8
	
		log -------------------- Abbasid lacking capital - nobles' financial help
		console_command add_money abbasid, 5000

		if not I_IsFactionAIControlled abbasid
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
log -------------------- Abbasid evolution: (3) xxxx ----------------------------------------

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

;---------------------- player ---------------------------

monitor_event BuildingCompleted TrueCondition
	and NumBuildingsCompletedFaction landowners2 > 2				; should be replaced with counter number_landowners2_built
	and FactionType abbasid

	if I_IsFactionAIControlled abbasid
		terminate_monitor
	end_if

	if I_NumberOfSettlements abbasid > 3
		and I_TurnNumber > 60
		and not I_EventCounter abbasids_rise > 0

		historic_event abbasids true
		terminate_monitor
	end_if
end_monitor

monitor_event PreFactionTurnStart FactionType abbasid

	if I_IsFactionAIControlled abbasid
		terminate_monitor
	end_if

	if I_TurnNumber > 70
		and not I_EventCounter abbasids_rise > 0

		historic_event abbasids true
		terminate_monitor
	end_if
end_monitor

monitor_event EventCounter EventCounterType abbasids_accepted
	and EventCounter = 1

	historic_event abbasids_rise

	add_events
		event	counter	first_mamluks_abbasid
		date	60
		event	counter	mamluks_established_abbasid
		date	100
	end_add_events

	set_event_counter abbasids_accepted 0
	terminate_monitor
end_monitor

monitor_event EventCounter EventCounterType abbasids_declined
	and EventCounter = 1

	add_events
		event	counter	first_mamluks_abbasid
		date	100
		event	counter	mamluks_established_abbasid
		date	140
	end_add_events

	set_event_counter abbasids_declined 0
	terminate_monitor
end_monitor

monitor_event EventCounter EventCounterType first_mamluks_abbasid
	and EventCounter = 1
	and I_EventCounter abbasids_rise = 0

	historic_event abbasids_rise

	terminate_monitor
end_monitor

;---------------------   AI --------------------------

monitor_event PreFactionTurnStart FactionType abbasid

	if not I_IsFactionAIControlled abbasid
		terminate_monitor
	end_if

	if I_TurnNumber > 70
		and I_NumberOfSettlements abbasid > 2

		historic_event abbasids_rise
		add_events
			event	counter	first_mamluks_abbasid
			date	60
			event	counter	mamluks_established_abbasid
			date	100
		end_add_events
		terminate_monitor
	end_if
end_monitor


;========================================================================================================================
;====================================================================================================== FATIMID EVOLUTION
;---------- Fatimid Evolution (Baqt, Fatimid to Ayyubid, Ayyubid to Mamluk)

set_event_counter fatimid2_x 0				; turn 80:
set_event_counter fatimid3_x 0				; turn 160:
set_event_counter fatimid4_x 0				; turn 400
set_event_counter fatimid5_x 0				; turn 500:
set_event_counter fatimid6_x 0				; turn 600

set_event_counter egypt_baqt_payed 21		; this script starts working after 21 turns
set_event_counter ayyubids_rise 0

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType Egypt

	;-------------------- Egypt stability : (A) Financial support after a LOSS OF THE CAPITAL
	;-------------------   (this script is needed because losing the capital usually paralyses financially the faction)
	
	if not I_SettlementOwner Cairo = egypt
	and not I_EventCounter player_size_huge == 1
	and I_NumberOfSettlements egypt > 0
	and I_NumberOfSettlements egypt < 8
	
		log -------------------- Egypt lacking capital - nobles' financial help
		console_command add_money egypt, 5000

		if not I_IsFactionAIControlled egypt
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if
	
	
log -------------------- Egypt evolution: (0 1) INFORMATION ----------------------------------------

log -------------------- Egypt evolution: (21) BAQT ----------------------------------------

	if RandomPercent > 80
	and I_EventCounter egypt_baqt_payed < 1						; this counter decreases by 1 per turn in another script
	and I_SettlementOwner Qus = egypt
	and I_EventCounter ayyubids_rise < 1 
	and I_EventCounter faction_size_smallormedium > 0			; well, to limit support of a strong faction... but maybe not?
	
		log --- script start : Baqt (1) EVENT AND MONEY --------------------

		reveal_tile 304, 2
		add_events
			event	historic EGYPT_MAKURIAN_BAQT factions { egypt, jerusalem, zengid, abbasid, }
			date	0
			position 304, 2											; Faras
		end_add_events	
		if I_EventCounter EGYPT_MAKURIAN_BAQT_accepted > 0
			disable_cursor
			disable_shortcuts true
			ui_indicator_remove 0
			ui_indicator_remove 1
			snap_strat_camera 304, 2
			zoom_strat_camera 1.0
			reveal_tile 304, 2		; Faras
			campaign_wait 1
			ui_indicator 0 arrow_up_right track_ground_3d 177 75 10 colour 255 0 0 period 3
			campaign_wait 4
			ui_indicator_remove 1
			campaign_wait 2
			ui_indicator_remove 0
			enable_cursor
			disable_shortcuts false
		end_if
		if I_EventCounter EGYPT_MAKURIAN_BAQT_declined > 0
			ui_indicator_remove 0
			ui_indicator_remove 1
		end_if
		hide_all_revealed_tiles

		console_command add_money egypt, 3000
		set_event_counter egypt_baqt_payed 21

		log ------ script : Baqt (2) BLACK ARMY AS PART OF BAQT  --------------
	
		; ONCE OTHER BLACK UNITS ARE RECODED TO BE MAKURIAN, CHANGE COMPOSITION OF THE Army !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	
		if RandomPercent < 50
			log ------ spawn baqt army
			spawn_army
				faction egypt
				character	Talq, general, age 28, x 315, y 18
					unit		African Tribal Warriors		exp 4 armour 0 weapon_lvl 0
					unit		African Tribal Warriors		exp 0 armour 0 weapon_lvl 0
			end
		end_if

	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;====================================================================================================== Fatimid to Ayyubid transition
;---------- Fatimid to Ayyubid transition

;---------------------- player ---------------------------

monitor_event BuildingCompleted TrueCondition
	and NumBuildingsCompletedFaction landowners2 > 2				; should be replaced with counter number_landowners2_built
	and FactionType egypt

	if I_IsFactionAIControlled egypt
		terminate_monitor
	end_if

	if I_TurnNumber > 40
		and not I_EventCounter ayyubids_rise > 0
		and not I_EventCounter mamluks_rise > 0

		historic_event ayyubids true
		terminate_monitor
	end_if
end_monitor

monitor_event PreFactionTurnStart FactionType egypt

	if I_IsFactionAIControlled egypt
		terminate_monitor
	end_if

	if not I_EventCounter ayyubids_rise > 0
		and not I_EventCounter mamluks_rise > 0

		if I_TurnNumber > 50
			and I_SettlementOwner Damascus = egypt

			historic_event ayyubids true
			terminate_monitor
		end_if

		if I_TurnNumber > 70
			historic_event ayyubids true
			terminate_monitor
		end_if
	end_if
end_monitor

monitor_event EventCounter EventCounterType ayyubids_accepted
	and EventCounter = 1

	historic_event ayyubids_rise
;        destroy_buildings egypt byzantine_mercenary_barracks false - this is an old coding, one need to care for the recruitment under ayyubids separately

	add_events
		event	counter	first_mamluks
		date	40
		event	counter	mamluks_established
		date	60
	end_add_events

	set_event_counter ayyubids_accepted 0
	terminate_monitor
end_monitor

monitor_event EventCounter EventCounterType ayyubids_declined
	and EventCounter = 1

	add_events
		event	counter	first_mamluks
		date	80
		event	counter	mamluks_established
		date	120
	end_add_events

	set_event_counter ayyubids_declined 0
	terminate_monitor
end_monitor

monitor_event EventCounter EventCounterType first_mamluks
	and EventCounter = 1
	and I_EventCounter ayyubids_rise = 0

	historic_event ayyubids_rise

	terminate_monitor
end_monitor

;---------------------   AI --------------------------

monitor_event PreFactionTurnStart FactionType egypt

	if not I_IsFactionAIControlled egypt
		terminate_monitor
	end_if

	if I_TurnNumber > 70
		and I_NumberOfSettlements egypt > 2
		and not I_EventCounter mamluks_rise > 0

		historic_event ayyubids_rise
		set_event_counter fatimid_evolution 1
		add_events
			event	counter	first_mamluks
			date	40
			event	counter	mamluks_established
			date	60
		end_add_events
		terminate_monitor
	end_if
end_monitor


;====================================================================================================== Fatimid to Ayyubid transition
;---------- Ayyubid to Mamluk transition
;  And is it the right faction? Or egypt?

monitor_event SettlementTurnStart SettlementName Damascus
	and FactionType mongols
	and I_EventCounter ayyubids_rise > 0
	and not I_EventCounter mamluks_rise > 0

	historic_event mamluks_rise
	terminate_monitor
end_monitor

monitor_event SettlementTurnStart SettlementName Damascus
	and FactionType mongols
	and I_EventCounter ayyubids_rise = 0
	and not I_EventCounter mamluks_rise > 0

	historic_event mamluks_rise
	set_event_counter first_mamluks 1
	set_event_counter mamluks_established 1
	terminate_monitor
end_monitor

monitor_event SettlementTurnStart SettlementName Aleppo
	and FactionType mongols
	and I_EventCounter ayyubids_rise > 0
	and not I_EventCounter mamluks_rise > 0

	historic_event mamluks_rise
	terminate_monitor
end_monitor

monitor_event SettlementTurnStart SettlementName Aleppo
	and FactionType mongols
	and I_EventCounter ayyubids_rise = 0
	and not I_EventCounter mamluks_rise > 0

	historic_event mamluks_rise
	set_event_counter first_mamluks 1
	set_event_counter mamluks_established 1
	terminate_monitor
end_monitor

monitor_event BecomesFactionLeader FactionType egypt
	and not I_EventCounter mamluks_rise > 0

	if I_FactionLeaderTrait egypt Royal_Blood_Mamluk = 1
		historic_event mamluks_rise
	end_if

	terminate_monitor
end_monitor



;======================================================================================================
;------------------------------------------------------------------------------------------------------
;-------- Events used for multiple factions
;------------------------------------------------------------------------------------------------------
;======================================================================================================

;---------- Coat of Arms counters
set_event_counter france_first_coat_of_arms_player 0
set_event_counter jerusalem_first_coat_of_arms_player 0
set_event_counter hre_first_coat_of_arms_player 0
set_event_counter england_first_coat_of_arms_player 0
set_event_counter scotland_first_coat_of_arms_player 0
set_event_counter denmark_first_coat_of_arms_player 0
set_event_counter norway_first_coat_of_arms_player 0
set_event_counter poland_first_coat_of_arms_player 0
set_event_counter hungary_first_coat_of_arms_player 0
set_event_counter sicily_first_coat_of_arms_player 0
set_event_counter venice_first_coat_of_arms_player 0
set_event_counter pisa_first_coat_of_arms_player 0
set_event_counter spain_first_coat_of_arms_player 0
set_event_counter portugal_first_coat_of_arms_player 0
set_event_counter aragon_first_coat_of_arms_player 0

;---------- Ostsiedlung counters	
set_event_counter hungary_ostsiedlung_player 0
set_event_counter poland_ostsiedlung_player 0
set_event_counter hre_ostsiedlung_player 0


;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event PreFactionTurnStart FactionType slave


;=======================================================================================================================
;=======================================================================================================================
;-------- Catholic Factions
;=======================================================================================================================
;=======================================================================================================================

;==========================================================================================================================	
;======================================================================================================  FIRST COAT OF ARMS
;---------- Coat of Arms event

; Allows building of the Tourney building.
; In this section there's the fall-back option for all factions for turn ca. 60.
; FURTHER MODDING, or TO DO :
;	- make it dependent on a successful crusade or something else.
; 	- make it specific for each faction.
	
	if not I_EventCounter FIRST_COAT_OF_ARMS == 1						; fall back
	and I_TurnNumber > 60												; in year 1167
	and RandomPercent < 10

		historic_event FIRST_COAT_OF_ARMS								; so important that should be known to the whole world

		if not I_IsFactionAIControlled france
			set_event_counter france_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled jerusalem
			set_event_counter jerusalem_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled hre
			set_event_counter hre_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled england
			set_event_counter england_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled scotland
			set_event_counter scotland_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled denmark
			set_event_counter denmark_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled norway
			set_event_counter norway_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled poland
			set_event_counter poland_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled hungary
			set_event_counter hungary_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled sicily
			set_event_counter sicily_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled venice
			set_event_counter venice_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled pisa
			set_event_counter pisa_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled spain
			set_event_counter spain_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled portugal
			set_event_counter portugal_first_coat_of_arms_player 1
		end_if

		if not I_IsFactionAIControlled aragon
			set_event_counter aragon_first_coat_of_arms_player 1
		end_if

	end_if

	if I_EventCounter FIRST_COAT_OF_ARMS == 1
	and not I_EventCounter GREAT_TOURNEY_ALLOWED == 1
	and RandomPercent < 5

		historic_event GREAT_TOURNEY_ALLOWED					; allows interactive event Great Tourney

	end_if


;===================================================================================================================
;======================================================================================================  OSTSIEDLUNG
;---------- Ostsiedlung event
;
; Historically it was essential for many factions in the Central Europe (Poland, Hungary, HRE, but also Denmark, Norway, Serbia, Kiev, Novgorod).
; It impacts on those factions through buildings' capabilities, but also through other mechanisms (eg. mercenaries).
; In this section there's the fall-back option for all factions for turn ca. 150.
; Three factions: Poland, Hungary, HRE have their own triggers to prompt it earlier.

	if not I_EventCounter OSTSIEDLUNG == 1								; fall back
	and I_TurnNumber > 140												; in year 1207
	and RandomPercent < 10

		historic_event OSTSIEDLUNG										; so important that should be known to the whole world

		if not I_IsFactionAIControlled hungary
			set_event_counter hungary_ostsiedlung_player 1
		end_if

		if not I_IsFactionAIControlled poland
			set_event_counter poland_ostsiedlung_player 1
		end_if

		if not I_IsFactionAIControlled hre
			set_event_counter hre_ostsiedlung_player 1
		end_if
		
	end_if

	; Baltic Crossbowmen available from Hanseatic buildings:
	if not I_EventCounter OSTSIEDLUNG_CROSSBOWMEN == 1
	and I_EventCounter OSTSIEDLUNG == 1
	and RandomPercent < 5
		historic_event OSTSIEDLUNG_CROSSBOWMEN factions { poland, hre, france, england, norway, denmark, hungary, lithuania, kievan_rus, russia, }
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@


;=======================================================================================================================
;=======================================================================================================================
;---------- Info on spheres of influence (activated on SettlementSelected)

; activated on "SettlementSelected" because otherwise (ie if triggered at the end of the Slave turn)
;	 they're pop out hidden by other events happening in the faction
; the coding is time-effective: only 1 trigger that is terminated on the first turn
; Warning: when a turn ends and a settlement is selected, then SettlementSelected fires at the beginning of the next turn
; so don't use add any more monitor in this way as below ("TrueCondition" / "SettlementIsLocal") - it's not reliable!

;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
monitor_event SettlementSelected SettlementIsLocal

	terminate_monitor									; TEMPORARY DIsable AS IT IS ONEROUS FOR TESTING - AND MABYE SHOULD BE DELETED ENTIRELY
	
	if not I_IsFactionAIControlled poland
		log ------------------------------------- POLAND
		disable_cursor
		hide_ui
		zoom_strat_camera 1
		campaign_wait 0.1
		move_strat_camera 219, 212
		point_at_settlement Krakow
		campaign_wait 1
		move_strat_camera 199, 214
		point_at_settlement Wroclaw
		campaign_wait 1
		move_strat_camera 194, 222
		point_at_settlement Poznan
		campaign_wait 1
		move_strat_camera 207, 236
		point_at_settlement Gdansk
		campaign_wait 1
		move_strat_camera 226, 224
		point_at_settlement Plock
		campaign_wait 1
		move_strat_camera 214, 216
		campaign_wait 1
		ui_flash_stop
		show_ui
		enable_cursor
		campaign_wait 0.2
		historic_event INFO_SPHERES_OF_INFLUENCE
		terminate_monitor
	end_if

	if not I_IsFactionAIControlled norway
		log ------------------------------------- NORWAY
		disable_cursor
		hide_ui
		zoom_strat_camera 1
		campaign_wait 0.1
		move_strat_camera 116, 281
		point_at_settlement Bergen
		campaign_wait 1
		move_strat_camera 154, 276
		point_at_settlement Oslo
		campaign_wait 1
		move_strat_camera 138, 276
		campaign_wait 1
		ui_flash_stop
		show_ui
		enable_cursor
		campaign_wait 0.2
		historic_event INFO_SPHERES_OF_INFLUENCE
		terminate_monitor
	end_if

	if not I_IsFactionAIControlled denmark
		log ------------------------------------- DENMARK
		disable_cursor
		hide_ui
		zoom_strat_camera 1
		campaign_wait 0.1
		move_strat_camera 141, 244
		point_at_settlement Ribe
		campaign_wait 1
		move_strat_camera 164, 246
		point_at_settlement Roskilde
		campaign_wait 1
		move_strat_camera 172, 247
		point_at_settlement Lund
		campaign_wait 1
		move_strat_camera 165, 247
		campaign_wait 1
		ui_flash_stop
		show_ui
		enable_cursor
		campaign_wait 0.2
		historic_event INFO_SPHERES_OF_INFLUENCE
		terminate_monitor
	end_if

	if not I_IsFactionAIControlled georgia
		log ------------------------------------- GEORGIA
		disable_cursor
		hide_ui
		zoom_strat_camera 1
		campaign_wait 0.1
		move_strat_camera 386, 151
		point_at_settlement Kutaisi
		campaign_wait 1
		move_strat_camera 400, 146
		point_at_settlement Tbilisi
		campaign_wait 1
		move_strat_camera 395, 146
		campaign_wait 1
		ui_flash_stop
		show_ui
		enable_cursor
		campaign_wait 0.2
		historic_event INFO_SPHERES_OF_INFLUENCE
		terminate_monitor
	end_if

end_monitor
;@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

]]


---@LATER reimplement support for factions elsewhere ?
--[[
	;-------------------- Poland stability : (A) Financial support after a LOSS OF THE CAPITAL
	;--- (this script is needed because losing the capital usually paralyses financially the faction)
	;---  this money is added before the economy script (levelling up the debts of the AI)"PreFactionTurnStart" while the former is 
	;--   this is good: otherwise the AI would plunge into debt spiral (in debt -> economy script levelling to 0 -> this script add money -> new recruitment -> more debt)
	
	if not I_SettlementOwner Krakow = poland			; Poland lost its capital
	and not I_EventCounter player_size_huge == 1		; if the player is too powerful, also certain AI factions should be able to get big
	and I_NumberOfSettlements poland > 0				; Poland exists
	and I_NumberOfSettlements poland < 8				; but it's not very powerful - if yes, then it should solve the issue without help
	
		log -------------------- Poland lacking capital - nobles' financial help
		console_command add_money poland, 5000
		set_event_counter poland_supported_due_to_capital_occupation 1
		
		if not I_IsFactionAIControlled poland
			historic_event NO_CAPITAL_SUPPORT
		end_if
	
	end_if

	
	;-------------------- Poland stability : (B) Support if the FACTION IS SMALL ------------
	; only for the AI
	; big support for 1-3 settlements, smaller for 3-9
	; helps to avoid the usual inability of the AI to defend if attacked from multiple directions


	if I_EventCounter faction_turn_poland == 1
	and I_IsFactionAIControlled poland
	and I_EventCounter poland_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Poland supported when the player is NOT POLAND
	
		if I_EventCounter faction_size_poland_small == 1
			console_command add_money poland, 5000
		end_if
		if I_EventCounter faction_size_poland_medium == 1
			console_command add_money poland, 2000
		end_if
	end_if
	
	if I_CompareCounter pl_ec_id == 13 ; HRE
	and I_EventCounter poland_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Poland supported when the player is HRE (add-on)
		
		if I_EventCounter faction_size_poland_small == 1
			console_command add_money poland, 3000
		end_if
		if I_EventCounter faction_size_poland_medium == 1
			console_command add_money poland, 1000
		end_if
	end_if

	if I_CompareCounter pl_ec_id == 24 ; kievan_rus
	and I_EventCounter poland_supported_due_to_capital_occupation < 1
	and I_EventCounter faction_size_smallormedium == 1
	and I_TurnNumber > 0
	
		log --- script start : Poland supported when the player is KIEV (add-on)
		
		if I_EventCounter faction_size_poland_small == 1
			console_command add_money poland, 3000
		end_if
		if I_EventCounter faction_size_poland_medium == 1
			console_command add_money poland, 1000
		end_if
	end_if

--]]

---@TODO also factioninfos should befired from elsewhere or redone in a awiki
--[[
	if not I_IsFactionAIControlled poland
		log -------------------- Poland evolution: (0 1) INFORMATION ----------------------------------------
		if I_TurnNumber == 0
			historic_event POLAND0_STARTING_INFO
		end_if
		if I_TurnNumber == 3
			historic_event POLAND1_REFORMS_INFO
		end_if
	end_if

	]]


return module