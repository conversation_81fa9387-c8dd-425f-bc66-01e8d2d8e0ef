----------------------------------------------------------------------------------------------------------------------------------
--- MARK: profiler
--- ----------------------------------------------------------------------------------------------------------------------------------

local profile = require('sship.profile')
profile.start()

----------------------------------------------------------------------------------------------------------------------------------
--- MARK: log setup
--- ----------------------------------------------------------------------------------------------------------------------------------

-- must go first before any other require statements so the global LOG can be used everywhere.
LOG = require('sship/sshipLog')
local log = LOG.new()
log.resetLogFile()

LOG_LEVELS = {
	aggressivity = 'debug',
	aiSupport = 'info',
	battleAI = 'info',
	battleModels = 'info',
	campaignAI = 'debug',
	campaignSetup = 'info',
	crusades = 'info',
	diplomacy = 'info',
	economy = 'info',
	factionsEvolutions = 'info',
	financial = 'info',
	globalEvents = 'trace',
	helpers = 'info',
	internalAffairs = 'debug',
	internationalAffairs = 'debug',
	main = 'info',
	mergeArmies = 'info',
	missions = 'info',
	mongols = 'info',
	navy = 'debug',
	nobleLadies = 'info',
	populationCap = 'info',
	reemergence = 'debug',
	settlementSituation = 'info',
	shortcuts = 'info',
	spoilsOfWar = 'info',
	traits = 'info',
	ui = 'debug',
}

log.level = LOG_LEVELS.main

----------------------------------------------------------------------------------------------------------------------------------
--- MARK: screen size
----------------------------------------------------------------------------------------------------------------------------------

local ffi = require("ffi")
ffi.cdef [[
typedef long LONG;
typedef void* HANDLE;
typedef HANDLE HWND;
typedef struct RECT {
LONG left;
LONG top;
LONG right;
LONG bottom;
} RECT;
typedef int BOOL;
typedef RECT *LPRECT;
BOOL GetClientRect(HWND hWnd,LPRECT lpRect);
HWND GetActiveWindow(void);
]]

----------------------------------------------------------------------------------------------------------------------------------
--- MARK: require statements
--- ----------------------------------------------------------------------------------------------------------------------------------

local helpers = require('sship.helpers')
local diplomacy = require('sship.diplomacy')
local mergeArmies = require('sship.mergeArmies')
local campaignSetup = require('sship.campaignSetup')
local aiSupport = require('sship.aiSupport')
---@LATER merge finances and economy files?
local financial = require('sship.financial')
local economy = require('sship.economy')
-- shortcuts.lua hold everything inside, even the eop function onShortcutTriggered()
require('sship.shortcuts')
require('sship/test')
local battleModels = require('sship.battleModels')
local missions = require('sship.missions')
local spoilsOfWar = require('sship.spoilsOfWar')
local aggressivity = require('sship.aggressivity')
local internalAffairs = require('sship.internalAffairs')
local internationalAffairs = require('sship.internationalAffairs')
local battleAI = require('sship.battleAI')
local campaignAI = require('sship.campaignAI')
local populationCap = require('sship.populationCap')
local globalEvents = require('sship.globalEvents')
local settlementSituation = require('sship.settlementSituation')
local navy = require('sship.navy')
local crusades = require('sship.crusades')
local nobleLadies = require('sship.nobleLadies')
local traits = require('sship.traits')
local remergence = require('sship.reemergence')
local mongols = require('sship.mongols')
local ui = require('sship.ui')

---MARK: WIP building browser

---@param level buildingLevel
function showCapabilities(level)
	local capabilityText = {}
	for text, index in pairs(buildingCapability) do
		print(text, index)
		capabilityText[index - 1] = text
	end
	print('table : '..table.concat(capabilityText, ', '))
	-- for i = 0, level.capabilityNum - 1 do
	-- 	local capability = level:getCapability(i)
	-- 	ImGui.BulletText(capabilityText[capability.capabilityID]..' : '..capability.bonus)
	-- end
end

function drawBuildingBrowser()
	ImGui.Begin('Building Brower')
	for i = 0, EDB.getBuildingNum() - 1 do
		local building = EDB.getBuildingByID(i)
		ImGui.SeparatorText(building.localizedName..'('..building.name..')')
		for j = 0, building.buildingLevelCount - 1 do
			local level = building:getBuildingLevel(j)
			local currentFaction = M2TW.campaign.currentFaction and M2TW.campaign.currentFaction.factionID or 1
			ImGui.Text(level:getLocalizedName(currentFaction)..'('..level.name..')')
			ImGui.BulletText('Cost '..level.buildCost..', build time : '..level.buildTime)
			showCapabilities(level)
		end
	end
	ImGui.End()
end

----------------------------------------------------------------------------------------------------------------------------------
-- MARK: load and save
----------------------------------------------------------------------------------------------------------------------------------

function onLoadSaveFile(paths)
	log.info('onLoadSaveFile triggered')
	campaignPopup = true;
	for _, path in pairs(paths) do
		if (string.find(path, "configTable.lua"))
		then
			-- Function from helper, load saved table
			SAVED_DATA = persistence.load(path);
		end
	end
end

---@return table savefiles a list of M2TWEOP save files
function onCreateSaveFile()
	log.info('onCreateSaveFile triggered')
	local savefiles = {};
	currentPath = M2TWEOP.getPluginPath();
	-- Function from helper, save our table
	persistence.store(currentPath .. "configTable.lua", SAVED_DATA);
	savefiles[1] = currentPath .. "configTable.lua";
	return savefiles;
end

function onCampaignMapLoaded()
	log.info('onCampaignMapLoaded triggered')
	local turnNumber = M2TW.campaign.turnNumber
	globalEvents.eraCounters(turnNumber)
	globalEvents.greatPeoples(turnNumber)
	globalEvents.leapYear(turnNumber)
	local currentFac = M2TW.campaign.currentFaction or M2TWEOP.getFactionRecord(1)
	if currentFac then
		---@TODO why this gives a null value for religion on creating new campaign? update eop before testing
		globalEvents.factionCounters(currentFac)
		globalEvents.isPlayer(currentFac)
		globalEvents.leaderStatus(currentFac)
		globalEvents.setPolicies(currentFac)
	end
	if M2TW.campaign then
		for i = 0, M2TW.campaign.numberOfFactions - 1 do
			local fac = M2TW.campaign:getFactionByOrder(i);
			ui.defineExtentColors(fac.factionRecord)
		end
	end
end

function onPluginLoad()
	log.always('onPluginLoad called')
	M2TWEOP.unlockGameConsoleCommands();
	-- grab screen size
	local window = ffi.C.GetActiveWindow()
	local rect = ffi.new("RECT")
	ffi.C.GetClientRect(window, rect)
	SCREEN.width = (rect.right-rect.left)
	SCREEN.height = (rect.bottom-rect.top)
	log.always('resolution : '..SCREEN.width..', '..SCREEN.height)
	---gta plan int => gta plan string
	GTA_PLANS = {}
	for key, value in ipairs(aiPlan) do
		print(value, key)
		GTA_PLANS[value] = key
	end
	if M2TW.campaign then
		for i = 0, M2TW.campaign.numberOfFactions - 1 do
			local fac = M2TW.campaign:getFactionByOrder(i);
			aggressivity.initAggr(fac)
			ui.defineExtentColors(fac.factionRecord)
		end
	end
end

----------------------------------------------------------------------------------------------------------------------------------
-- MARK: globals
----------------------------------------------------------------------------------------------------------------------------------

--- Table containing all data we want to save with the savegame
SAVED_DATA = {
	aggressivityFactions= {},
	civilWarWarningTurnsFactions = {},
	internalAffairs = {
		civilWarWarningTurns = 0
	},
	internationalAffairs = {
	factionsAttackedByPlayer = {}
	}
}

--- stores the screen dimensions
SCREEN = {width = 0, height = 0}

--- Stores fonts that can be used in-game by eop
FONTS = {}

----------------------------------------------------------------------------------------------------------------------------------
---MARK: miscellanous
----------------------------------------------------------------------------------------------------------------------------------
--[[
From Fynn:
onNewGameStart() -> as soon as you click start campaign button, nothing loaded yet
onNewGameLoaded() -> M2TW.stratMap and M2TW.campaign have finished initializing their fields
					(this happens before models and textures and all that)
onCampaignMapLoaded() -> finished loading campaign map including strat models and stuff, use for strat model related,
						fires every time you load the map including post battle etc
For use newGameStart to reset everything to defaults, newGameLoaded to initialize your own stuff, campaignMapLoaded for cas models
]]
local logSeparator = '===================================================================================================='

--- Called after the game has loaded to the main menu
function onGameInit()
	missions.loadRewards()
end

function onLoadingFonts()
	log.info('onLoadingFonts triggered')
	FONTS.kp14 = ImGui.GetIO().Fonts:AddFontFromFileTTF(M2TWEOP.getModPath().."/data/fonts/MedievalSharp-Regular.ttf", 32)
	FONTS.vinque = ImGui.GetIO().Fonts:AddFontFromFileTTF(M2TWEOP.getModPath().."/data/fonts/vinque-rg.ttf", 32)
end

function onNewGameStart()
	--be very careful to place here anything, some things are not processed
	log.always(logSeparator)
	log.always('NEW GAME CREATED (onNewGameStart triggered)')
	SAVED_DATA.internalAffairs.civilWarWarningTurns = 0
	SAVED_DATA.internationalAffairs.factionsAttackedByPlayer = {}
	--campaignAI.setFactors()
end


function onNewGameLoaded()
	log.always(logSeparator)
	log.always('NEW GAME LOADED (onNewGameLoaded triggered)')
	for i = 0, M2TW.campaign.numberOfFactions - 1 do
		local faction = M2TW.campaign:getFactionByOrder(i)
		campaignSetup.adjustKingsPurse(faction)
		campaignSetup.revealTilesFirstTurn(faction)
		aggressivity.initAggr(faction)
		internalAffairs.initCivilWarningTurns(faction)
		ui.defineExtentColors(faction.factionRecord)
	end
	globalEvents.setDifficulty()
end

function onReadGameDbsAtStart()
	log.info('onReadGameDbsAtStart triggered')
end


---MARK: genCaptureSettlements

-- Exports: character, settlement, targetSettlement, faction, targetFaction, regionID, characterType, religion
---@param eventData eventTrigger
function onGeneralCaptureSettlement(eventData)
	local attacker = eventData.faction
	local settlement = eventData.targetSettlement
	log.info('CAPTURED SETTLEMENT: faction '..attacker.name..' captured settlement '..settlement.name)
	settlementSituation.updateName(attacker, settlement)
	if attacker.isPlayerControlled == 1 then
		aggressivity.addAggr(attacker, settlement)
		aggressivity.checkAggr(attacker)
	end
end

---**Exports:** faction, religion
---@param eventData eventTrigger
function onFactionNewCapital(eventData)
	log.info('onFactionNewCapital triggered')
	local faction = eventData.faction
	financial.newCapitalCost(faction)
end

---------------------------------------------------------------------------------------------------------------------------------
-- MARK: turn starts
-- the events are ordered in the same order the game fire them
-- DO NOT change the order for the sake of code legibility ! --- Belovese
---------------------------------------------------------------------------------------------------------------------------------

local turnEndStart = 0
local turnTimes = {}

---@LATER add a wait to not count the time where a scroll is opened, nor the battle time
---@param turnNumber integer
function onChangeTurnNum(turnNumber)
	log.always(logSeparator)
	log.always('TURN NUMBER : '..turnNumber..'(onChangeTurnNum triggered)')

	local lastDuration = os.clock() - turnEndStart
	table.insert(turnTimes, lastDuration)
	log.always('Last turn time : '..lastDuration..', average turn time : '..helpers.getTableAverage(turnTimes)..' (out of '..#turnTimes..' turns)')
	
	log.always(profile.report(10))
	diplomacy.deadFactionsFix()
	globalEvents.eraCounters(turnNumber)
	globalEvents.greatPeoples(turnNumber)
	globalEvents.leapYear(turnNumber)
	nobleLadies.resetSpawnCounter()
end

--- **Exports:** faction, religion
---@param eventData eventTrigger
function onPreFactionTurnStart(eventData)
	local faction = eventData.faction
	log.always(logSeparator)
	log.always('Pre faction turn start for '..helpers.logFaction(faction))
	battleModels.updateModels(faction)
	if faction.isPlayerControlled == 0 then
		economy.aiTurnStart(faction)
	end
	-- loop through all stacks
	log.debug("Sorting stacks..")
	for i= 0, faction.armiesNum - 1 do
		local stack = faction:getArmy(i);
		if stack then stack:sortStack(sortType.categoryClass, sortType.experience, sortType.soldierCount) end
	end
	globalEvents.factionCounters(faction)
	globalEvents.manageRecruitPools(faction)
	globalEvents.militaryOrders(faction)
	globalEvents.isPlayer(faction)
	globalEvents.leaderStatus(faction)
	globalEvents.setPolicies(faction)
	nobleLadies.checkForSpawn(faction)
	if faction.isPlayerControlled == 1 then mongols.mongolsInvasion() end
end


--**Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCharacterTurnStart(eventData)
	local character = eventData.character
	log.info('Turn start for '..helpers.logCharacter(character))
end

--- **Exports:** settlement, faction, regionID, religion
---@param eventData eventTrigger
function onSettlementTurnStart(eventData)
	local settStruct = eventData.settlement
	log.info('Turn start for '..helpers.logSettlement(settStruct))
	aiSupport.destroySpecialBuildings(settStruct)
end

---@param eventData eventTrigger
function onFactionTurnStart(eventData)
	local campaign = M2TW.campaign
	local faction = eventData.faction
	log.always(logSeparator)
	log.always('Turn start for '..helpers.logFaction(faction))
	--checkForSpawn(faction)
	-- give imgui some transparency and faction's colors
	if campaign.currentFaction ~= nil then
		local record = M2TWEOP.getFactionRecord(campaign.currentFaction.factionID)
		local red = record.primaryColorRed/255
		local green = record.primaryColorGreen/255
		local blue = record.primaryColorBlue/255
		local alpha = 0.5
		ImGui.PushStyleColor(ImGuiCol.WindowBg, 0, 0, 0, 1)
		ImGui.PushStyleColor(ImGuiCol.ScrollbarGrabActive, red, green, blue, alpha)
		ImGui.PushStyleColor(ImGuiCol.ScrollbarGrab, red, green, blue, alpha)
		ImGui.PushStyleColor(ImGuiCol.Separator, red, green, blue, alpha)
		ImGui.PushStyleColor(ImGuiCol.SeparatorActive, red, green, blue, 1.0)
		ImGui.PushStyleColor(ImGuiCol.SeparatorHovered, 0, 0, 0, 1.0)
		ImGui.PushStyleColor(ImGuiCol.FrameBg, 0, 0, 0, 1)
  		ImGui.PushStyleColor(ImGuiCol.ChildBg, red, green, blue, alpha/2)
	end
	navy.addFleetMovePoints(eventData)	-- apply movement points depending on technological advance and culture
end



---------------------------------------------------------------------------------------------------------------------------------
-- MARK: turn ends
-- the events are ordered in the same order the game fire them
-- DO NOT change the order for the sake of code legibility ! --- Belovese
-- note that *character turn end* (CTE) and *character turn end in settlement* (CTEIS) fire one after the other for each character
-- so the order is:
--      * character 1 CTE
--      * character 1 CTEIS (if applicable)
--      * character 2 CTE
--      * character 2 CTEIS (if applicable)
--      * and so on for every character
---------------------------------------------------------------------------------------------------------------------------------

---Exports: character, settlement, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCharacterTurnEnd(eventData)
	local character = eventData.character
	log.info('Turn end for '..helpers.logCharacter(character))
end

---Exports: settlement, faction, regionID, religion
---@param eventData eventTrigger
function onSettlementTurnEnd(eventData)
	local settlement = eventData.settlement
	log.info('Turn end for '..helpers.logSettlement(settlement))
end

---Exports: character, settlement, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCharacterTurnEndInSettlement(eventData)
	local character = eventData.character
	local settlement = eventData.settlement
	log.info('Turn end in settlement for '..helpers.logCharacter(character)..' in '..helpers.logSettlement(settlement))
end

---Exports: faction, religion
---@param eventData eventTrigger
function onFactionTurnEnd(eventData)
	local faction = eventData.faction
	log.info('Turn end for '..helpers.logFaction(faction))
	if faction.isPlayerControlled == 1 then
	   	aiSupport.changeAILabel(faction)
		aggressivity.coolingAggr(faction)
		internalAffairs.setHeir(faction)
		--internalAffairs.removeDeadSpouses(faction)
		internalAffairs.mainFunctionCivilWars(faction)
		aggressivity.printAggr(faction)
		internalAffairs.printCivilWarningTurns()
		crusades.removeCrusaderReturningTrait(eventData)
		crusades.constantinopleJihad()
		crusades.historicCalltoHolyWar()
		internalAffairs.usurperCalculation(faction)
		
	end
	if faction.isPlayerControlled == 0 then
		mergeArmies.processAIArmies(faction)
		settlementSituation.healthBldgRestore(faction)
		internalAffairs.mainFunctionCivilWars(faction)
		if faction.name == 'slave' then remergence.factionReemerge() end	-- just so it is runned once, in the end of turn

	end
	internationalAffairs.checkWarEnd(eventData)
	
end 

---------------------------------------------------------------------------------------------------------------------------------
-- MARK: selection
---------------------------------------------------------------------------------------------------------------------------------

---Exports: settlement, faction, regionID, religion
---@param eventData eventTrigger
function  onSettlementSelected(eventData)
    local settlement = eventData.settlement
	log.info('Selected '..helpers.logSettlement(settlement))
	-- for i=0,settlement.recruitmentPoolCount - 1 do
	-- 	local pool = settlement:getSettlementRecruitmentPool(i)
	-- 	local unit = M2TWEOPDU.getEduEntry(pool.eduIndex)
	-- 	if unit ~= nil then print(unit.localizedName, pool.availablePool)
	-- 	else print( 'no unit found', pool.eduIndex, pool.availablePool) end
	-- end
end

--- **Exports:** character, targetSettlement, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCharacterSelected(eventData)
	local charRec = eventData.character
	log.info('Selected '..helpers.logCharacter(charRec))
    local xChar, yChar = charRec.character.xCoord, charRec.character.yCoord
    local tile = M2TW.stratMap.getTile(xChar, yChar)
    local ownerfac = tile.factionID
    log.info("attackPenalty: tile owner ID is : "..tostring(ownerfac))
	log.info("Characters move points : "..tostring(charRec.character.movePoints))
	traits.newLeaderTraits(charRec)
end

---Exports: character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onEnemyCharacterSelected(eventData)
	local character = eventData.character
	log.info('Selected '..helpers.logCharacter(character))
end

---@param char character
function onCharacterClicked(char)
	log.info('Clicked on '..helpers.logCharacter(char.characterRecord))
	local stack = char.army
    if stack then
        stack:sortStack(sortType.categoryClass, sortType.experience, sortType.soldierCount)
        log.info('sorted a stack')
    end
end


---------------------------------------------------------------------------------------------------------------------------------
-- MARK: leaders and heirs
---------------------------------------------------------------------------------------------------------------------------------

--- **Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onBecomesFactionLeader(eventData)
	if eventData.faction.leader == nil then return end
	local character = eventData.character
	local faction = eventData.faction
	log.info('onBecomesFactionLeader triggered for '..helpers.logCharacter(character))
	globalEvents.leaderStatus(faction)
	globalEvents.setPolicies(faction)
	financial.throneChangeCost(faction, 'leader')
end

--- **Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCeasedFactionLeader(eventData)
	if eventData.faction.leader == nil then return end
	local character = eventData.character
	log.info('onCeasedFactionLeader triggered for '..helpers.logCharacter(character))
	local faction = eventData.faction
	if character.deathType ~= 0 then internalAffairs.memorizeRoyals(faction) end
	internationalAffairs.diplomaticReset(faction)
	if faction.deadStatus == 0 and faction.heir == nil then
		--Replacement for inactive onFacionLeaderDestroyed event
	end
end

--- **Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function  onBecomesFactionHeir(eventData)
	if eventData.faction.heir == nil then return end
	local character = eventData.character
	local faction = eventData.faction
	log.info('onBecomesFactionHeir triggered for '..helpers.logCharacter(character))
	internalAffairs.recalculationFLDied(faction)
	financial.throneChangeCost(faction, 'heir')
	--if character.deathType ~= 0 then internalAffairs.setHeir(faction) end
end

--- **Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function  onCeasedFactionHeir(eventData)
	if eventData.faction.heir == nil then return end
	local character = eventData.character
	log.info('onCeasedFactionHeir triggered for '..helpers.logCharacter(character))
end

--- **Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCharacterComesOfAge(eventData)
	local faction = eventData.faction
	local character = eventData.character
	if faction.isPlayerControlled == 1 and character:isHeir() == true then
		log.debug("InternalA: the heir just become of age: "..character.fullName)
		internalAffairs.recalculationFHofAge(faction)
	end
end

--- **Exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onCharacterBecomesAFather(eventData)
	local faction = eventData.faction
	local character = eventData.character
	if faction.isPlayerControlled == 1 and character:isLeader() == true then
		log.debug("InternalA: the leader has a new child!: "..character.fullName)
	end
end

---Exports: character, faction, regionID, characterType, religions
---@param eventData eventTrigger
function onCharacterMarries(eventData)
	local faction = eventData.faction
	local character = eventData.character
	log.info('onCharacterMarries triggered for '..helpers.logCharacter(character))
	if faction.isPlayerControlled == 1 then
		log.debug("InternalA: one of the FM is married, recalculate the heir: "..character.fullName)
		internalAffairs.setHeir(faction)
	end
end

---Exports: character, faction, regionID, characterType, religions
---@param eventData eventTrigger
function onCharacterMarriesPrincess(eventData)
	local character = eventData.character
	log.info('onCharacterMarries triggered for '..helpers.logCharacter(character))
end

function onOfferedForAdoption(eventData)
	local faction = eventData.faction
	local character = eventData.character
	log.info('onOfferedForAdoption triggered for '..helpers.logCharacter(character))
	if faction.isPlayerControlled == 1 then
		log.debug("InternalA: offered for adoption, recalculate the heir: "..character.fullName)
		internalAffairs.setHeir(faction)
	end
end

---A faction has been destroyed.
---**DO NOT USE** as it doesn't trigger ! 
---**exports:** character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onLeaderDestroyedFaction(eventData)
	log.always('onLeaderDestroyedFaction: '..helpers.logFaction(eventData.faction))
end

---------------------------------------------------------------------------------------------------------------------------------
-- MARK: Battle - Campaign
---------------------------------------------------------------------------------------------------------------------------------

---Exports : faction, religion
---@param eventData eventTrigger
function onPreBattlePanelOpen(eventData)
	log.info('onPreBattlePanelOpen triggered')
	battleAI.randomBattleFiles()
end

-- This triggers in any attack, captain vs captain as well, peace or war, but not when attacking a settlment
---Exports : character, targetCharacter, faction, targetFaction, regionID, characterType, targetCharacterType, religion, targetReligion
---@param eventData eventTrigger
function onGeneralAssaultsGeneral(eventData)
	internationalAffairs.attackPenalty(eventData)
end

-- This triggers in any attack, captain vs captain as well, attacking a settlement as well
function onFactionWarDeclared(eventData)
	internationalAffairs.declaredWar(eventData)
end

-- macaras: transgression TC_INSTIGATE_SIEGE triggers this function for example, the type of transg. is written in the eventData.resourceDescription
--- Exports : faction, targetFaction, resourceDescription, religion, targetReligion
---@param eventData eventTrigger
 function onTransgression(eventData)
	if eventData.resourceDescription == 'TC_INSTIGATE_SIEGE' and eventData.faction.isPlayerControlled == 1 then internationalAffairs.siegePenalty(eventData) end
	if eventData.resourceDescription == 'TC_UNDECLARED_ATTACK' and (eventData.faction.isPlayerControlled == 1 or eventData.targetFaction.isPlayerControlled == 1) then
		navy.admiralFleetAncillaryBonusCheck(eventData)  --it may be not a navy battle, but there is a condition inside this function
	end
end

--fynn: onPostBattle is a character event, the faction is the faction the character belongs to,
-- the event fires for every character involved in the battle not just once
--- Exports : character, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onPostBattle(eventData)
	local character = eventData.character
	log.info('onPostBattle triggered for '..helpers.logCharacter(character))
	if character.faction.isPlayerControlled == 1 then
		spoilsOfWar.generousCharacter = character
		log.always('Post battle triggered for human player - calling spoilsOfWar.callCampSackedEvent')
		spoilsOfWar.callCampSackedEvent()
	end
end

---------------------------------------------------------------------------------------------------------------------------------
-- MARK: Crusade/Jihad
---------------------------------------------------------------------------------------------------------------------------------
--- Exports: character, targetSettlement, faction, targetFaction, regionID, targetRegionID, characterType, religion, targetReligion, crusade
--- @param eventData eventTrigger
function onGeneralTakesCrusadeTarget(eventData)
	local character = eventData.character
	local faction = eventData.faction
	log.info('onGeneralTakesCrusadeTarget triggered for '..helpers.logCharacter(character))
	crusades.bringBackCrusadeArmies(eventData)	--also means jihad
	-- disband units only if the winner is catholic-> it was a crusade not a jihad
	if eventData.faction.religion == 0 then crusades.disbandCrusadeUnits(eventData) end 
end
--[[
function onGeneralJoinCrusade(eventData)
--something
end

--Exports: targetSettlement, targetFaction, targetRegionID, targetReligion, crusade/jihad?
--- @param eventData eventTrigger
function onCrusadeCalled(eventData)

	if eventData.targetFaction.religion ~= 2 then 	-- 0= catholic, 1=orthodox, 2=islam
		crusades.jihadInProgress = true
		log.debug("+Crusade: jihad is called. ")
	elseif eventData.targetFaction.religion == 2 then -- 2=islam
		crusades.crusadeInProgress = true
		log.debug("+Crusade: crusade is called. ")
	end

--something here
end

-- Exports: targetSettlement, targetRegionID, crusade/jihad?
--- @param eventData eventTrigger
function onCrusadeEnds(eventData)

--something here
end
]]
---------------------------------------------------------------------------------------------------------------------------------
-- MARK: CampaignAI
---------------------------------------------------------------------------------------------------------------------------------

---@param personality aiPersonality
function onSetProductionControllers(personality)
log.info('CampaignAI: Event onSetProductionControllers, character: '..personality.aiFaction.faction.name)
end


--Exports: character, settlement, faction, regionID, characterType, religion
---@param eventData eventTrigger
function onAgentCreated(eventData)
log.info('CampaignAI: Event onAgentCreated, character: '..eventData.character.fullName..', faction : '..eventData.faction.localizedName.."-----")
end
---@param eventData eventTrigger
function onBuildingCompleted(eventData)
	if eventData.priorBuild and eventData.priorBuild.building then 
	log.info("CampaignAI: settlement: "..eventData.settlement.name.." building: "..eventData.priorBuild.building:getName().." -----")
	end
end
---@param ltgd aiLongTermGoalDirector
function onCalculateLTGD(ltgd)
	campaignAI.setFactors()
	campaignAI.mainCampaignAI(ltgd)
end
---------------------------------------------------------------------------------------------------------------------------------
-- MARK: onEventCounter
---------------------------------------------------------------------------------------------------------------------------------

---Exports: eventCounter
---@param eventData eventTrigger
function onEventCounter(eventData)
	-- get the name and value of the triggered counter
	local counterName = eventData.eventCounter
	local counterValue =  M2TWEOP.getScriptCounter(counterName)
	log.info('Counter : '..counterName..' has been set to : '..counterValue)
	-- Below may go different counters
	if counterName == 'BATTLE_CAMP_SACKED_ENEMY_accepted' and counterValue > 0 then
			spoilsOfWar.lootCamp(1)  --add money and generous trait
			M2TWEOP.setScriptCounter("BATTLE_CAMP_SACKED_ENEMY_accepted", 0)
		elseif counterName == 'BATTLE_CAMP_SACKED_ENEMY_declined' and counterValue > 0 then
			spoilsOfWar.lootCamp(0)  --add money
			M2TWEOP.setScriptCounter("BATTLE_CAMP_SACKED_ENEMY_declined", 0)
	end
	if counterName == 'CONSTANTINOPLE_SACK_CRUSADE_accepted' and counterValue > 0 then
			M2TWEOP.setScriptCounter("CONSTANTINOPLE_SACK_CRUSADE_accepted", 0)
			crusades.veniceSacksConstantinople()
		elseif counterName == 'CONSTANTINOPLE_SACK_CRUSADE_declined' and counterValue > 0 then
			M2TWEOP.setScriptCounter("CONSTANTINOPLE_SACK_CRUSADE_declined", 0)
	end
end

---------------------------------------------------------------------------------------------------------------------------------
-- MARK: missions
---------------------------------------------------------------------------------------------------------------------------------

--- **Exports:** character, faction, regionID, characterType, missionDetails, religion
---@param eventData eventTrigger
function onLeaderMissionSuccess(eventData)
	local missionName = eventData.missionDetails.missionName
	local rewardName = eventData.missionDetails.paybackName
	log.info('mission success : '..missionName..' (reward : '..rewardName..')')
	---@LATER could use missionName to have more control over the rewards ? 
	---@LATER at some point redo the scroll poping for these special pissions
	if rewardName == 'council_scripted_unit' then
		log.warn('mission passed the paybackID test')
		missions.pick(eventData.faction.name)
	end
end


---------------------------------------------------------------------------------------------------------------------------------
-- MARK: Scrolls open&closed
---------------------------------------------------------------------------------------------------------------------------------

---Exports: faction, religion
---@param eventData eventTrigger
function onDiplomacyPanelOpen(eventData)
	local faction = eventData.faction
	log.info('Diplomacy pannel opened by '..helpers.logFaction(faction))
	diplomacy.deadFactionsFix()
end

---Exports: resourceDescription
---@param eventData eventTrigger
function onScrollOpened(eventData)
	local scrollName = eventData.resourceDescription
	log.info('Scroll opened : '..scrollName)
	if scrollName == 'advanced_settlement_info_scroll' then DRAW_CAPITAL_CHANGE = true end
	--- when pressing ESC in campaign
	if scrollName == 'end_game_scroll' then DRAW_DEBUG_TOOLS = true end

end

---Exports: resourceDescription
---@param eventData eventTrigger
function onScrollClosed(eventData)
	local scrollName = eventData.resourceDescription
	log.info('Scroll closed : '..scrollName)
	if scrollName == 'advanced_settlement_info_scroll' then DRAW_CAPITAL_CHANGE = false end
	--- when pressing ESC in campaign
	if scrollName == 'end_game_scroll' then DRAW_DEBUG_TOOLS = false end
	if scrollName == 'diplomacy_scroll' then internationalAffairs.checkWarEnd(eventData) end

end

---------------------------------------------------------------------------------------------------------------------------------
---MARK: onButtonPressed
---------------------------------------------------------------------------------------------------------------------------------

---Exports: resourceDescription
---@param eventData eventTrigger
function  onButtonPressed(eventData)
	local buttonName = eventData.resourceDescription
	log.info('onButtonPressed : '..buttonName)
	if buttonName == 'end_turn' then
		turnEndStart = os.clock()
	end
end
---------------------------------------------------------------------------------------------------------------------------------
---MARK: tick and draw
---------------------------------------------------------------------------------------------------------------------------------

local lastSecondPassed = 0
-- ImGui window Triggers
DRAW_CAPITAL_CHANGE = false
DRAW_SPOILS = false
DRAW_DEBUG_TOOLS = false

local showProfiler = false
local showWarmongerPoints = false
local showCapitalName = false
local showCounters = false
local showOptions = false
local showBuildingsBrowser = false
local showFinancialReport = false

local changedExtent = false

function onCampaignTick()
	if showProfiler then helpers.profiler.getData() end
	if M2TW.selectionInfo.hoveredCharacter ~= nil and not changedExtent then
		ui.updateExtentColor()
		changedExtent = true
	else
		changedExtent = false
	end
end

function onBattleTick()
	if showProfiler then helpers.profiler.getData() end
	-- battleAI.chooseStrategy()
	-- local secondsRounded = math.floor(M2TW.battle.secondsPassed)
	-- if secondsRounded ~= lastSecondPassed then
	--     -- print(lastSecondPassed)
	--     lastSecondPassed = secondsRounded
	--     if lastSecondPassed % 3 == 0 then
	-- 		print(M2TW.battle.tickCount, M2TW.battle.ticksSinceBattleStart, M2TW.battle.secondsPassed)
	--         print(M2TW.battle.sides[2].battleAIPlan.gtaPlan)
	--     end
	-- end
end


function draw()
	-- if M2TW.battle.battleState > 1 then battleAI.visualizer() end
	local width, height = SCREEN.width, SCREEN.height
	if DRAW_CAPITAL_CHANGE then financial.drawCapitalChangeCost() end	
	if DRAW_DEBUG_TOOLS then
		ImGui.SetNextWindowPos(0, 0, ImGuiCond.Appearing)
		ImGui.SetNextWindowSize(width*0.32, height*0.77, ImGuiCond.Appearing)
		ImGui.Begin('debug_tools')
		showWarmongerPoints = ImGui.Checkbox('warmonger_points', showWarmongerPoints)
		showCapitalName = ImGui.Checkbox('capital names', showCapitalName)
		showCounters = ImGui.Checkbox('globalcounters', showCounters)
		showOptions = ImGui.Checkbox('detailed options', showOptions)
		showProfiler = ImGui.Checkbox('profiler', showProfiler)
		showBuildingsBrowser = ImGui.Checkbox('buildings browser', showBuildingsBrowser)
		showFinancialReport = ImGui.Checkbox('financial report', showFinancialReport)
		ImGui.End()
	end
	if showOptions then ui.optionsWindow() end
	if showWarmongerPoints then
		ImGui.SetNextWindowPos(width*0.1, height*0.1, ImGuiCond.FirstUseEver)
		ImGui.Begin('Warmonger')
		local fac = M2TW.campaign.currentFaction
		ImGui.Text('Current Warmonger points : '..SAVED_DATA.aggressivityFactions[fac.name].aggr_pts)
	end
	if showFinancialReport then financial.drawReport() end
	if showCapitalName then
		ImGui.SetNextWindowPos(width*0.1, height*0.1, ImGuiCond.FirstUseEver)
		ImGui.Begin('capital_names')
		local fac = M2TW.campaign.currentFaction
		ImGui.Text('Capital name should be : '..fac:getSettlement(0).name..' ('..fac:getSettlement(0).localizedName..')')
	end
	if showCounters then
		ImGui.SetNextWindowPos(width*0.1, height*0.1, ImGuiCond.FirstUseEver)
		globalEvents.debugValues()
	end
	if showBuildingsBrowser then drawBuildingBrowser() end
	if showProfiler then ui.profilerWindow() end
	-- triggering EOP console and reload
	if (ImGui.IsKeyPressed(ImGuiKey.GraveAccent))
	and (ImGui.IsKeyDown(ImGuiKey.LeftCtrl))
	then
		M2TWEOP.toggleConsole()
	elseif (ImGui.IsKeyPressed(ImGuiKey.GraveAccent))
	and (ImGui.IsKeyDown(ImGuiKey.LeftAlt))
	then
		M2TWEOP.toggleDeveloperMode()
	elseif (ImGui.IsKeyPressed(ImGuiKey.R))
		and (ImGui.IsKeyDown(ImGuiKey.LeftCtrl))
		and (ImGui.IsKeyDown(ImGuiKey.LeftShift))
	then
		M2TWEOP.restartLua()
	end
end

function test()
	for i = 0, M2TW.campaign.settlementNum - 1 do
		print('sett n° '..i)
		local settStruct = M2TW.campaign:getSettlement(i)
		aiSupport.destroySpecialBuildings(settStruct)
	end
end