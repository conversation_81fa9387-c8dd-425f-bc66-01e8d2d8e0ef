<?xml version="1.0" encoding="utf-8"?>
<!--ReallyBadAI v5.x by Germanicu<PERSON> @www.twcenter.net, January 2012-->
<!--This is an integral part of ReallyBadAI Battle System and shouldn't be randomly altered.-->
<!--Support: http://www.twcenter.net/forums/showthread.php?t=257970 /Third Age: Total War/ http://www.twcenter.net/forums/showthread.php?t=253132 /Stainless Steel/-->
<!--Freeware - non-private use only by permission.-->
<config>
  <!-- global combat balancing factors -->
  <combat-balancing>
    <missile-target-accuracy>
      <infantry>0.75</infantry>
      <cavalry>1.25</cavalry>
      <elephants>1.5</elephants>
    </missile-target-accuracy>
    <melee-hit-rate>3.0</melee-hit-rate>
  </combat-balancing>
  <!-- per unit task configuration -->
  <unit-tasks>
    <!-- wall reform - used for split up units -->
    <wall-reform>
      <!-- once more than this number are queued up, split across the other ladders -->
      <queue-length-before-split>4</queue-length-before-split>
    </wall-reform>
    <!-- unformed charge -->
    <unformed-charge>
      <!-- proportion of unit that will charge before unit task will finish -->
      <finish-proportion-infantry>0.80</finish-proportion-infantry>
      <finish-proportion-cavalry>0.80</finish-proportion-cavalry>
    </unformed-charge>
  </unit-tasks>
  <unit>
    <!-- phalanx configuration -->
    <phalanx>
      <intercept-range>15</intercept-range>
    </phalanx>
    <!-- skirmish configuration -->
    <skirmish>
      <infantry>
        <default>
          <!-- ignore targets at a distance greater than the maximum range times this scale factor -->
          <max-range-scale>4.0</max-range-scale>
          <!-- must skirmish if within this range -->
          <min-range>30</min-range>
          <!-- stop at this distance if the enemy is blocking the path -->
          <min-stopping-range>45</min-stopping-range>
          <!-- time to react to being intercepted -->
          <collision-reaction-time>1</collision-reaction-time>
          <!-- retreat buffer time -->
          <retreat-time>1</retreat-time>
          <!-- don't skirmish until attackers are within this fraction of the missile range -->
          <range-factor>
            <moving>1.1</moving>
            <shooting>0.80</shooting>
          </range-factor>
        </default>
        <gunpowder>
          <!-- ignore targets at a distance greater than the maximum range times this scale factor -->
          <max-range-scale>4.0</max-range-scale>
          <!-- must skirmish if within this range -->
          <min-range>35</min-range>
          <!-- stop at this distance if the enemy is blocking the path -->
          <min-stopping-range>45</min-stopping-range>
          <!-- time to react to being intercepted -->
          <collision-reaction-time>2</collision-reaction-time>
          <!-- retreat buffer time -->
          <retreat-time>1</retreat-time>
          <!-- don't skirmish until attackers are within this fraction of the missile range -->
          <range-factor>
            <moving>1.1</moving>
            <shooting>0.80</shooting>
          </range-factor>
        </gunpowder>
      </infantry>
      <cavalry>
        <default>
          <!-- ignore targets at a distance greater than the maximum range times this scale factor -->
          <max-range-scale>4.0</max-range-scale>
          <!-- must skirmish if within this range -->
          <min-range>35</min-range>
          <!-- stop at this distance if the enemy is blocking the path -->
          <min-stopping-range>45</min-stopping-range>
          <!-- time to react to being intercepted -->
          <collision-reaction-time>2</collision-reaction-time>
          <!-- retreat buffer time -->
          <retreat-time>1</retreat-time>
          <!-- don't skirmish until attackers are within this fraction of the missile range -->
          <range-factor>
            <moving>1.1</moving>
            <shooting>0.8</shooting>
          </range-factor>
          <!-- reaction time for cantabrian circle -->
          <cantabrian-reaction-time>4</cantabrian-reaction-time>
        </default>
        <gunpowder>
          <!-- ignore targets at a distance greater than the maximum range times this scale factor -->
          <max-range-scale>4.0</max-range-scale>
          <!-- must skirmish if within this range -->
          <min-range>40</min-range>
          <!-- stop at this distance if the enemy is blocking the path -->
          <min-stopping-range>50</min-stopping-range>
          <!-- time to react to being intercepted -->
          <collision-reaction-time>2</collision-reaction-time>
          <!-- retreat buffer time -->
          <retreat-time>1</retreat-time>
          <!-- don't skirmish until attackers are within this fraction of the missile range -->
          <range-factor>
            <moving>1.1</moving>
            <shooting>0.8</shooting>
          </range-factor>
          <!-- reaction time for cantabrian circle -->
          <cantabrian-reaction-time>4</cantabrian-reaction-time>
        </gunpowder>
      </cavalry>
    </skirmish>
    <!-- missile units can exceed their max range by this percentage -->
    <max-missile-range-extension>20</max-missile-range-extension>
  </unit>
  <settlements>
    <rules>
      <!-- rules relating to plaza capture -->
      <plaza-capture>
        <!-- time the plaza needs to be held for to capture -->
        <time-limit>4.0</time-limit>
        <!-- ratio of attackers to defenders to hold a plaza -->
        <soldier-ratio>0.85</soldier-ratio>
      </plaza-capture>
    </rules>
  </settlements>
  <movement>
    <ladders>
      <!-- should run be the default? -->
      <default-run>1</default-run>
      <!-- start distance for the blockout region -->
      <entry-blockout-inner>0.0</entry-blockout-inner>
      <!-- end distance for the blockout region -->
      <entry-blockout-outer>25.0</entry-blockout-outer>
      <!-- limit on number of soldiers in the blockout region -->
      <number-in-blockout>1</number-in-blockout>
      <!-- distance soldiers should stop at if the entrance is blocked (in metres squared) -->
      <stand-off-distance>81.0</stand-off-distance>
      <!-- distance within which soldiers will step onto the line (in metres squared) -->
      <step-on-distance>25.0</step-on-distance>
      <!-- queue length limit before movement will be stopped (in number of soldiers -->
      <maximum-queue-length>16</maximum-queue-length>
      <!-- distance soldiers should stop at if the queue is too long (in metres squared) -->
      <long-queue-stand-off-distance>400</long-queue-stand-off-distance>
      <!-- when the entrance to a line is blocked all of the soldiers are stopped. -->
      <!-- once the entrance is clear then the first x soldiers will be restarted moving. x is set below -->
      <restart-threshold>6</restart-threshold>
      <!-- when the entrance to a line is block any soldiers beyond this position in the queue will be halted. -->
      <halt-threshold>-1</halt-threshold>
      <!-- do the soldiers have to enter in a precise order? -->
      <precise-order>0</precise-order>
    </ladders>
    <siege-tower-ladders>
      <!-- should run be the default? -->
      <default-run>1</default-run>
      <!-- start distance for the blockout region -->
      <entry-blockout-inner>0.0</entry-blockout-inner>
      <!-- end distance for the blockout region -->
      <entry-blockout-outer>30.0</entry-blockout-outer>
      <!-- limit on number of soldiers in the blockout region -->
      <number-in-blockout>5</number-in-blockout>
      <!-- distance soldiers should stop at if the entrance is blocked (in metres squared) -->
      <stand-off-distance>15.0</stand-off-distance>
      <!-- distance within which soldiers will step onto the line (in metres squared) -->
      <step-on-distance>10.0</step-on-distance>
      <!-- queue length limit before movement will be stopped (in number of soldiers -->
      <maximum-queue-length>16</maximum-queue-length>
      <!-- distance soldiers should stop at if the queue is too long (in metres squared) -->
      <long-queue-stand-off-distance>40.0</long-queue-stand-off-distance>
      <!-- when the entrance to a line is blocked all of the soldiers are stopped. -->
      <!-- once the entrance is clear then the first x soldiers will be restarted moving. x is set below -->
      <restart-threshold>7</restart-threshold>
      <!-- when the entrance to a line is block any soldiers beyond this position in the queue will be halted. -->
      <halt-threshold>-1</halt-threshold>
      <!-- do the soldiers have to enter in a precise order? -->
      <precise-order>0</precise-order>
    </siege-tower-ladders>
    <corridors>
      <!-- should run be the default? -->
      <default-run>1</default-run>
      <!-- start distance for the blockout region -->
      <entry-blockout-inner>0.0</entry-blockout-inner>
      <!-- end distance for the blockout region -->
      <entry-blockout-outer>30.0</entry-blockout-outer>
      <!-- limit on number of soldiers in the blockout region -->
      <number-in-blockout>32</number-in-blockout>
      <!-- distance soldiers should stop at if the entrance is blocked (in metres squared) -->
      <stand-off-distance>15.0</stand-off-distance>
      <!-- distance within which soldiers will step onto the line (in metres squared) -->
      <step-on-distance>25.0</step-on-distance>
      <!-- queue length limit before movement will be stopped (in number of soldiers -->
      <maximum-queue-length>64</maximum-queue-length>
      <!-- distance soldiers should stop at if the queue is too long (in metres squared) -->
      <long-queue-stand-off-distance>20.0</long-queue-stand-off-distance>
      <!-- when the entrance to a line is blocked all of the soldiers are stopped. -->
      <!-- once the entrance is clear then the first x soldiers will be restarted moving. x is set below -->
      <restart-threshold>24</restart-threshold>
      <!-- when the entrance to a line is block any soldiers beyond this position in the queue will be halted. -->
      <halt-threshold>24</halt-threshold>
      <!-- do the soldiers have to enter in a precise order? -->
      <precise-order>0</precise-order>
    </corridors>
  </movement>
  <audio-triggers>
    <!-- minimum time in seconds between warnings for each unit that it is idle and under fire from missiles -->
    <min-time-between-attacked-idle-warnings>60.0</min-time-between-attacked-idle-warnings>
  </audio-triggers>
</config>